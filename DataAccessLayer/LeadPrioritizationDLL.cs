﻿using System;
using DataAccessLibrary;
using DataHelper;
using System.Data;
using System.Data.SqlClient;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using PropertyLayers;
using System.Collections.Generic;
using System.Linq;
using ReadXmlProject;
using Helper;
using Newtonsoft.Json;
using System.Text;
using System.Runtime.Caching;
using Redis;
using MongoDB.Bson;

namespace DataAccessLayer
{
    public class LeadPrioritizationDLL
    {
        public static DataSet GetGroupNameandID(long UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserID", UserId);
                string query = "[MTX].[getGroupandProcessID]";
                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, query, sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(UserId), 0, ex.ToString(), "getGroupNameandID", "LeadPrioritizationDLL", "Communication", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            return ds;
        }

        public static List<PriorityModel> GetAgentActiveLeads(long UserId, short ProductId, bool IsHealthRenewalGrp, OneLeadParams oneLeadParams, bool productCheck = false, short GroupID = 0)
        {
            List<PriorityModel> lstPriorityModel = new();
            var _LeadPriorityConstants = GetPriorityConfigByProduct(ProductId, IsHealthRenewalGrp, GroupID);
            short CBShowTm = _LeadPriorityConstants.ActiveCBShowTime;

            List<Int32> HNIGroupIDs = "HNIGroupIDs".AppSettings().Split(',').Select(Int32.Parse).ToList();
            int CallBackWaitTime = Convert.ToInt32("CallBackWaitTime".AppSettings());

            DateTime ct = DateTime.Now;
            short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
            short WeekMaxAttempts = _LeadPriorityConstants.WeekMaxAttempts;

            try
            {
                //long leadid = 600123348;
                MongoHelper _CommDB = new(SingletonClass.OneLeadDB());
                var query = Query.And
                                        (
                                       //Query<PriorityModel>.EQ(p => p.LeadID, leadid),
                                       Query<PriorityModel>.EQ(p => p.User.UserID, UserId),
                                       Query<PriorityModel>.EQ(p => p.IsActive, true));

                if (productCheck)
                    query = Query.And(query, Query<PriorityModel>.EQ(p => p.ProductID, ProductId));

                lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), limit: 2000);


                lstPriorityModel = lstPriorityModel.Where(x =>
                                            (x.ActiveLeadSet != null && x.ActiveLeadSet.Count > 0)
                                        && (
                                                    (!string.IsNullOrEmpty(x.LeadSource) && x.LeadSource.ToUpper().Equals("RENEWAL"))
                                                 || (x.IsBooked == false)
                                                 || (x.CallBack != null && x.CallBack.CBtime > DateTime.Now.Date.AddMinutes(-30))
                                                 || (x.Revisit != null && x.Revisit.ts > DateTime.Now.Date.AddMinutes(-30) && x.Revisit.RevisitType == RevisitType.Ctc)
                                            )
                                        ).ToList();


                lstPriorityModel = lstPriorityModel.Where(x =>
                                            (IsCustomerReqCallback(x, CallBackWaitTime))
                                          || (
                                              (!string.IsNullOrEmpty(x.LeadSource)) // Reopen Lead Not allowed
                                              && (x.User.FirstAssignedOn.Date != DateTime.MinValue.Date)
                                              && (BypassWeekExhaustedCheck(x, CBShowTm) || x.Call == null || x.Call.NANC_Attempts < 4 || (Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(ct.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) / 7) + 1) != x.Call.Current_Week) || (x.Call.Current_Week == 1 ? x.Call.Week_Attempt < Week1MaxAttempts : (x.Call.Week_Attempt < WeekMaxAttempts))) //week attempts
                                              && (x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || (x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysNANCAttempt < 4)) // valid unanswered attempts not to picked (max 4)
                                              && (x.Call == null || x.CustomerCalltime.Date < DateTime.Now.Date || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerNANCAttempt < 4)) // valid unanswered attempts not to picked (max 4)
                                                                                                                                                                                                // && (x.Call == null || x.CustomerCalltime.Date < DateTime.Now.Date || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerTodayNANC < 10)) // valid unanswered attempts not to picked (max 4)
                                              && (x.Call == null || x.CustomerCalltime.Date < DateTime.Now.Date || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerTodayNANC < 6)) // EXPERIMENT: Do not allow call after 6 customer UA (unanswered attempts)
                                              && (
                                                    (ct.Subtract(x.SkippingTime).TotalMinutes > x.SkipDurationHrs)
                                                    || (x.Revisit != null && x.Revisit.ts > x.SkippingTime && x.PageName != "videomeet")// skip duration over or Revisit after Skip Time), do not pick if videomeet revisit
                                                    || (x.CallBack != null && x.CallBack.CBtime < x.SkippingTime.AddMinutes(x.SkipDurationHrs) // CB Is berore skip over time
                                                                                               && ct.Subtract(x.CallBack.CBtime).TotalMinutes > CBShowTm * -1  // bringing starts from cb- 5 mints time(cover active +passive)
                                                                                               && x.SkippingTime < x.CallBack.CBtime.AddMinutes(CBShowTm * -1))) //  skip not after cb-15
                                               && (x.ProductID == 117 || IsCorrectTimeToCall(x.Country, x.NRIAreaCode, (x.Country == 378 && !string.IsNullOrEmpty(x.NriCity)) ? x.NriCity : null))// Time Zone check to Non Motor Product
                                               && (x.IsAppointed == false) // If IsAppointed is false only then consider
                                               && (x.Call == null || x.DNC == null || (ct.Date.Subtract(x.DNC.ts.Date).TotalDays > x.DNC.CoolingPeriod)) // Do not Call
                                               && !(x.CallBack != null && x.CallBack.CallBackType == CallBackTypeEnum.CustRequested
                                               && x.CallBack.CBtime > DateTime.Now.AddMinutes(CallBackWaitTime))
                                               && (oneLeadParams.Source == null || oneLeadParams.Source != "appprogressive" || x.IsBooked == false)
                                                && ((x.ProductID != 131) ||
                                                    !(x.ProductID == 131 && !string.IsNullOrEmpty(x.LeadSource) && !string.IsNullOrEmpty(x.Utm_source) &&
                                                     string.Equals(x.LeadSource, "ExternalSources", StringComparison.OrdinalIgnoreCase) &&
                                                     string.Equals(x.Utm_source, "Medicalassociation", StringComparison.OrdinalIgnoreCase)))
                                           )
                                      ).ToList();


            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(UserId), 0, ex.ToString(), "GetAgentActiveLeads", "LeadPrioritizationDLL", "Communication", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            return lstPriorityModel;
        }

        private static bool BypassWeekExhaustedCheck(PriorityModel x, short CBShowTm)
        {
            DateTime ct = DateTime.Now;
            try
            {
                // callback set by advisor and callback is not set today and totalTT > 3min and there is no call within 30 minutes of callback
                if (x.CallBack != null && x.CallBack.CBtime < x.SkippingTime.AddMinutes(x.SkipDurationHrs) // CB Is berore skip over time
                    && ct.Subtract(x.CallBack.CBtime).TotalMinutes > CBShowTm * -1  // bringing starts from cb- 5 mints time(cover active +passive)
                    && x.SkippingTime < x.CallBack.CBtime.AddMinutes(CBShowTm * -1)
                    && x.CallBack.ts < DateTime.Now.Date
                    && x.Call != null && x.Call.TotalTT > 180 && x.Call.calltime < x.CallBack.CBtime.AddMinutes(-1 * 30)
                )
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", x.LeadID, ex.ToString(), "GetAgentActiveLeads", "LeadPrioritizationDLL", "Communication", "BypassWeekExhaustedCheck", string.Empty, DateTime.Now, DateTime.Now);
            }
            return false;
        }

        private static bool IsCustomerReqCallback(PriorityModel x, int CallBackWaitTime)
        {
            try
            {
                if (x.CallBack != null && x.CallBack.CallBackType == CallBackTypeEnum.CustRequested
                    && x.CallBack.CBtime < DateTime.Now.AddMinutes(CallBackWaitTime)
                    && (x.Call == null || x.Call.calltime < x.CallBack.CBtime.AddMinutes(-1 * CallBackWaitTime))
                )
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", x.LeadID, ex.ToString(), "GetAgentActiveLeads", "LeadPrioritizationDLL", "Communication", "IsCustomerReqCallback", string.Empty, DateTime.Now, DateTime.Now);
            }
            return false;
        }

        public static bool IsCorrectTimeToCall(short CountryId, short AreaCode = 0, string leadCity = null)
        {
            DateTime CurrentDate = DateTime.Now;
            DateTime requestTime = DateTime.Now;
            DateTime startTime;
            DateTime EndTime;

            if (CountryId == 0)
                return true;

            try
            {
                // check Timezone for NRI city
                var CityTimeZoneDict = MasterData.GetCityTimeZone();
                if (!string.IsNullOrEmpty(leadCity) && CityTimeZoneDict.ContainsKey(leadCity))
                {
                    NriCityTimeZone cityTZ = CityTimeZoneDict.GetValueOrDefault(leadCity);

                    if (cityTZ != null && (!string.IsNullOrEmpty(cityTZ.TimeDiffFromIST)))
                    {
                        if (cityTZ.StartTime > cityTZ.EndTime && CurrentDate.TimeOfDay < cityTZ.EndTime)
                            startTime = CurrentDate.Date.AddDays(-1).Add(cityTZ.StartTime);
                        else
                            startTime = CurrentDate.Date.Add(cityTZ.StartTime);
                        EndTime = cityTZ.StartTime < cityTZ.EndTime ? CurrentDate.Date.Add(cityTZ.EndTime) : CurrentDate.Date.AddDays(1).Add(cityTZ.EndTime);
                        if (startTime <= CurrentDate && EndTime >= CurrentDate)
                            return true;
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "IsCorrectTimeToCall_City_error", "OneLead", "LeadPrioritizationDLL", CountryId.ToString(), "", requestTime, DateTime.Now);
            }

            try
            {
                List<CountryTimeZone> lstZones = MasterData.GetCountryTimeZone();
                CountryTimeZone zone = null;
                if (lstZones != null)
                {
                    if (AreaCode > 0 && Convert.ToString(AreaCode).Length >= 3)
                        zone = lstZones.Where(x => x.CountryId == CountryId && x.AreaCode == AreaCode).SingleOrDefault();
                    zone ??= lstZones.Where(x => x.CountryId == CountryId && x.AreaCode == 0).SingleOrDefault();
                }
                if (zone == null || zone.CountryId == 0)
                    return true;
                if (zone.StartTime > zone.EndTime && CurrentDate.TimeOfDay < zone.EndTime)
                    startTime = CurrentDate.Date.AddDays(-1).Add(zone.StartTime);
                else
                    startTime = CurrentDate.Date.Add(zone.StartTime);
                EndTime = zone.StartTime < zone.EndTime ? CurrentDate.Date.Add(zone.EndTime) : CurrentDate.Date.AddDays(1).Add(zone.EndTime);

                if (startTime <= CurrentDate && EndTime >= CurrentDate)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "IsCorrectTimeToCall_error", "OneLead", "LeadPrioritizationDLL", CountryId.ToString(), "", requestTime, DateTime.Now);

            }
            return true;
        }

        public static productPriorityconstant GetPriorityConfigByProduct(short productID, bool renewal = false, short GroupID = 0)
        {
            List<short> LeadPriorityProduct = "LeadPriorityProduct".AppSettings().Split(',').Select(short.Parse).ToList();
            List<short> PaymentGroups = "HealthPaymentGroups".AppSettings().Split(',').Select(short.Parse).ToList();
            List<short> ChatRenewalGroups = "MotorChatGroups".AppSettings().Split(',').Select(short.Parse).ToList();
            List<short> ChatIBRenewalGroups = "ChatIBRenewalGroups".AppSettings().Split(',').Select(short.Parse).ToList();
            List<short> HealthFosGroups = "HealthFOSGroups".AppSettings().Split(',').Select(short.Parse).ToList();
            List<short> HealthProducts = new() { 106, 118, 130 };

            List<short> RenwalProducts = new() { 2, 117, 131, 101, 139 };
            if (HealthProducts.Contains(productID))
                productID = 2;

            else if (!LeadPriorityProduct.Contains(productID))
                productID = 117;


            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            if (RenwalProducts.Contains(productID) && PaymentGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "PAYMENT").SingleOrDefault();
            else if (RenwalProducts.Contains(productID) && HealthFosGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "FOS").SingleOrDefault();
            else if (productID == 117 && ChatRenewalGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "CHATRENEWAL").SingleOrDefault();
            else if (productID == 117 && ChatIBRenewalGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "CHATIBRENEWAL").SingleOrDefault();
            else if (RenwalProducts.Contains(productID) && renewal)
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "RENEWAL").SingleOrDefault();
            else
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct == "").SingleOrDefault();
        }

        public static UserNext5Leads GetUserNxt5LeadsFromMongo(long UserId)
        {
            string Key = RedisCollection.Next5Leads() + ":" + UserId;
            UserNext5Leads UserNext5Leads = new() { UserId = UserId };

            var obj = MatrixRedisHelper.GetRedisData(Key);
            if (obj != null)
            {
                UserNext5Leads = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
            }

            return UserNext5Leads;
        }

        public static void InsertNext5Leads(long UserId, UserNext5Leads oUserNext5Leads)
        {
            MatrixRedisHelper.SetRedisData(RedisCollection.Next5Leads() + ":" + UserId, JsonConvert.SerializeObject(oUserNext5Leads), new TimeSpan(7, 0, 0));
        }

        public static void UpdateNext5Leads(long UserId, UserNext5Leads oUserNext5Leads)
        {
            string Key = RedisCollection.Next5Leads() + ":" + UserId;
            MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(oUserNext5Leads), new TimeSpan(7, 0, 0));
        }

        public static void UpdateRestFlag(List<PriorityModel> lstPriorityModel)
        {
            MongoHelper objCommDB = new(SingletonClass.OneLeadDB());
            foreach (PriorityModel lead in lstPriorityModel)
            {
                UpdateBuilder<PriorityModel> update = null;
                IMongoQuery query = null;
                query = Query<PriorityModel>.EQ(x => x.LeadID, lead.LeadID);
                update = Update<PriorityModel>.Set(x => x.IsLastBucketRest, true);
                if (update != null)
                {
                    objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }
            }
        }

        public static List<PriorityModel> GetAgentBookedLeads(long UserId, short ProductId, bool IsHealthRenewalGrp)
        {
            List<PriorityModel> lstPriorityModel = new();
            var _LeadPriorityConstants = GetPriorityConfigByProduct(ProductId, IsHealthRenewalGrp);
            short CBShowTm = _LeadPriorityConstants.ActiveCBShowTime;

            DateTime ct = DateTime.Now;
            try
            {
                MongoHelper _CommDB = new(SingletonClass.OneLeadDB());
                var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId),
                                        Query<PriorityModel>.EQ(p => p.IsBooked, true),
                                        Query<PriorityModel>.EQ(p => p.IsActive, true));
                lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection());

                lstPriorityModel = lstPriorityModel.Where(x =>
                                            (
                                            (ct.Subtract(x.SkippingTime).TotalMinutes > x.SkipDurationHrs)
                                            || (x.Revisit != null && x.Revisit.ts > x.SkippingTime)// skip duration over or Revisit after Skip Time)
                                            || (x.CallBack != null && x.CallBack.CBtime < x.SkippingTime.AddMinutes(x.SkipDurationHrs) // CB Is berore skip over time
                                                                   && ct.Subtract(x.CallBack.CBtime).TotalMinutes > CBShowTm * -1  // bringing starts from cb- 5 mints time(cover active +passive)
                                                                   && x.SkippingTime < x.CallBack.CBtime.AddMinutes(CBShowTm * -1)
                                                )
                                        ) //  skip not after cb-15
                                       && (x.ProductID == 117 || IsCorrectTimeToCall(x.Country))// Time Zone check to Non Motor Product

                                        ).ToList();

            }
            catch (Exception)
            {

            }
            return lstPriorityModel;
        }

        public static PriorityModel GetSelectedData(PriorityModel X, LeadCategoryEnum _LeadCategoryEnum, short Counter)
        {
            return new PriorityModel { LeadID = X.LeadID, CustName = X.CustName, LeadCreatedOn = X.LeadCreatedOn, Call = X.Call, CallBack = X.CallBack, Revisit = X.Revisit, LeadCategory = _LeadCategoryEnum, LeadPoints = X.LeadPoints, CustID = X.CustID, ProductID = X.ProductID, LeadStatus = X.LeadStatus, PrevPolicyExpDate = X.PrevPolicyExpDate, SkippingTime = X.SkippingTime, SkipDurationHrs = X.SkipDurationHrs, User = X.User, EmailOnly = X.EmailOnly, Counter = Counter, LeadRank = X.LeadRank, LeadPriorityScore = X.LeadPriorityScore, Reason = X.Reason, LeadSource = X.LeadSource, InvestmentTypeId = X.InvestmentTypeId };
        }


        public static List<OneLeadScoreMapping> GetLeadScoreMapping()
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            //SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            string strQuery = "SELECT Type,MinValue,MaxValue,Score,ProductID FROM MTX.OneLeadScoreMapping";
            DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return (from dr in ds.Tables[0].AsEnumerable()
                    select new OneLeadScoreMapping
                    {

                        Type = dr.Field<string>("Type"),
                        Score = dr.Field<decimal>("Score"),
                        MinValue = dr.Field<short>("MinValue"),
                        MaxValue = dr.Field<short>("MinValue"),
                        ProductID = dr.Field<short>("ProductID")
                    }).ToList();

        }

        public static string CalculateCallShift(DateTime callDate)
        {
            string callShift = "X";// default value
            try
            {
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();

                byte callingHr = 0;
                if (callDate > DateTime.MinValue)
                {
                    callingHr = Convert.ToByte(callDate.Hour);
                    if (callingHr >= _LeadPriorityConstants.MorningShift && callingHr < _LeadPriorityConstants.AfterNoonShift)
                        return "M";
                    else if (callingHr >= _LeadPriorityConstants.AfterNoonShift && callingHr < _LeadPriorityConstants.EveningShift)
                        return "A";
                    else if (callingHr >= _LeadPriorityConstants.EveningShift)
                        return "E";
                }
            }
            catch (Exception)
            {

            }

            return callShift;
        }

        public static long UpDateNoLeadPopUp(long UserId)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[1];
                sqlparm[0] = new SqlParameter("@UserID", UserId);

                string query = "UPDATE CRM.LoginDetails SET NoLeadPopTime =GETDATE() WHERE UserID=@UserID AND CreatedOn > CAST(GETDATE() AS DATE) AND NoLeadPopTime IS NULL";
                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.Text, query, sqlparm);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(UserId), 0, ex.ToString(), "UpDateNoLeadPopUp", "UpDateNoLeadPopUp", "LeadPrioritizationDLL", "", "", requestTime, DateTime.Now);
            }
            return 1;
        }

        public static void InsertUpdateNext5Leads(long UserId, UserNext5Leads oUserNext5Leads, bool insertData)
        {
            if (insertData)
                InsertNext5Leads(UserId, oUserNext5Leads);
            else
                UpdateNext5Leads(UserId, oUserNext5Leads);
        }


        public static List<PriorityModel> GetAgentAllLeads(long UserId, short ProductId, bool showCallableLeads, bool isRenewalAgent)
        {

            List<PriorityModel> leads = null;
            DateTime requestTime = DateTime.Now;
            try
            {
                MongoHelper _CommDB = new(SingletonClass.OneLeadDB());

                if (isRenewalAgent && ProductId == 2)
                {
                    var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId), Query<PriorityModel>.EQ(p => p.IsActive, true));

                    var filteredDocuments = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection()).Where(x => x.ActiveLeadSet.Count > 0).Take(500).ToList();

                    var results = new List<PriorityModel>();

                    foreach (var document in filteredDocuments)
                    {
                        foreach (var activeLead in document.ActiveLeadSet)
                        {
                            var priorityModel = new PriorityModel
                            {
                                LeadID = activeLead,
                                CustName = document.CustName,
                                CustID = document.CustID,
                                ProductID = document.ProductID,
                                star = document.star,
                                DNC = (document.DNC != null && document.DNC.ts != DateTime.MinValue && DateTime.Now.Subtract(document.DNC.ts).Days <= document.DNC.CoolingPeriod) ? new DncData { CoolingPeriod = document.DNC != null ? document.DNC.CoolingPeriod : Convert.ToInt16(0), ts = document.DNC.ts } : null,
                                Call = document.Call != null ? new CallData { calltime = document.Call != null ? document.Call.calltime : DateTime.MinValue, uid = document.Call != null ? document.Call.uid : 0 } : null,
                                CountryName = GetCountryName(document, UserId)
                            };
                            results.Add(priorityModel);
                        }
                    }
                    leads = results.OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
                else if (!showCallableLeads)
                {
                    var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId), Query<PriorityModel>.EQ(p => p.IsActive, true));
                    leads = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), limit: 2000)
                               .Where(x => x.ActiveLeadSet.Count > 0)
                               .Select(x => new PriorityModel {
                                   LeadID = x.LeadID, CustName = x.CustName,
                                   CustID = x.CustID, ProductID = x.ProductID,
                                   star = x.star,
                                   DNC = (x.DNC != null && x.DNC.ts != DateTime.MinValue && DateTime.Now.Subtract(x.DNC.ts).Days <= x.DNC.CoolingPeriod) ? new DncData { CoolingPeriod = x.DNC != null ? x.DNC.CoolingPeriod : Convert.ToInt16(0), ts = x.DNC.ts } : null,
                                   Call = x.Call != null ? new CallData() { calltime = x.Call != null ? x.Call.calltime : DateTime.MinValue, uid = x.Call != null ? x.Call.uid : 0 } : null,
                                   CountryName = GetCountryName(x, UserId)
                               }).OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
                else
                {
                    UpDateNoLeadPopUp(Convert.ToInt64(UserId));

                    DateTime CurrentTime = DateTime.Now;
                    DateTime DayAfterTomorrowTime = DateTime.Now.AddDays(2);
                    LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                    short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                    var _ProductLeadPriorityConstants = GetPriorityConfigByProduct(ProductId);
                    short ActiveRevisitShowTime = _ProductLeadPriorityConstants.ActiveRevisitShowTime;
                    byte NANCMaxAttemInDiffShifts = _ProductLeadPriorityConstants.NANCMaxAttemInDiffShifts;
                    byte NANCMax1DayAttempt = _ProductLeadPriorityConstants.NANCMax1DayAttempt;
                    byte NANCLastCallDayGap = _ProductLeadPriorityConstants.NANCLastCallDayGap;
                    byte UnAnsweredDaysGap = _ProductLeadPriorityConstants.UnAnsweredDaysGap;
                    short Week1MaxAttempts = _ProductLeadPriorityConstants.Week1MaxAttempts;
                    short WeekMaxAttempts = _ProductLeadPriorityConstants.WeekMaxAttempts;
                    List<short> ExcessiveProducts = new() { 2, 7, 115 };


                    var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId),
                                     Query<PriorityModel>.EQ(p => p.IsActive, true)
                                     );
                    leads = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), limit: 2000)
                               .Where(x => x.ActiveLeadSet.Count > 0
                                            && (x.User.FirstAssignedOn != DateTime.MinValue)
                                            && (x.Call == null || x.Call.NANC_Attempts < 4 || (Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(CurrentTime.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) / 7) + 1) != x.Call.Current_Week) || (x.Call.Current_Week == 1 ? x.Call.Week_Attempt < Week1MaxAttempts : (x.Call.Week_Attempt < WeekMaxAttempts))) //week attempts
                                            && (x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || (x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysNANCAttempt < 4)) // valid unanswered attempts not to picked (max 4) 
                                            && (x.Call == null || x.CustomerCalltime.Date < DateTime.Now.Date || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerNANCAttempt < 4)) // valid customer unanswered attempts not to picked (max 4) 
                                            && (x.Call == null || x.CustomerCalltime.Date < DateTime.Now.Date || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerTodayNANC < 10)) // valid customer unanswered attempts not to picked (max 10) 
                                            &&
                                           (
                                            (x.Call == null) ||
                                            (x.Call.TalkTime > 0) ||
                                            (
                                              !(x.Call != null && x.Call.TotalTT == 0 && x.Call.CallAttempts > 2 && (CurrentTime.Subtract(x.LeadCreatedOn).TotalDays <= UnAnsweredDaysGap) && (!(((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt)) || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date) || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap))))
                                              && (!(((CurrentTime - x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes < _ProductLeadPriorityConstants.InvisibleTimeNANC) && (x.Revisit == null || (CurrentTime.Subtract(x.Revisit.ts).TotalMinutes > ActiveRevisitShowTime))))
                                             )
                                           )
                                           && (x.Call == null || !ExcessiveProducts.Contains(x.ProductID) || x.Call.LastNminuteNANCeAttempts < 2 || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 120 && x.Call.LastNminuteNANCeAttempts == 2)
                                                                                                  || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 180 && x.Call.LastNminuteNANCeAttempts > 2)) //When consecutive 2 unanswered aqttempts
                                                                                                                                                                                                        //&& (x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.LeadID % toomanyattemptfactor > 0 || !ExcessiveProducts.Contains(x.ProductID) || x.Call.TodaysAttempt < 5 || (x.Call.TodaysAttempt == 5 && x.Call.TodayTalktime >= 300) || (x.Call.TodaysAttempt > 5 && x.Call.lastNCallTT >= 300)) // too many attempt restriction id 5 attempts are done  removed on 22/05-2024- amit chouhan                                         
                                           && (x.Call == null || x.DNC == null || (DateTime.Now.Date.Subtract(x.DNC.ts.Date).TotalDays > x.DNC.CoolingPeriod)) // Do not call leads should not be come into 

                                     )
                               .Select(x => new PriorityModel { LeadID = x.LeadID, CustName = x.CustName, CustID = x.CustID, ProductID = x.ProductID, Call = x.Call, LeadStatus = GetLeadStatus(x), CountryName = GetCountryName(x, UserId), PrevPolicyExpDate = x.PrevPolicyExpDate, LeadCreatedOn = x.LeadCreatedOn, Revisit = x.Revisit, CallBack = x.CallBack }).OrderByDescending(x => x.LeadCreatedOn).ToList();

                }

            }

            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", UserId, ex.ToString(), "GetAgentAllLeads", "OneleadCoreAPI", "LeadPrioritizationBLL", "", string.Empty, requestTime, DateTime.Now);
            }

            return leads;
        }

        public static LeadStatusData GetLeadStatus(PriorityModel obj)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();

            if (obj.LeadStatus.SubStatusID > 0)
            {
                obj.LeadStatus.SubStatusName = _LeadPriorityConstants.subStatusList.FirstOrDefault(x => x.Key == obj.LeadStatus.SubStatusID).Value;
            }

            if (obj.Call == null)
                return obj.LeadStatus;

            bool v = int.TryParse(("TotalTalkTime" + obj.ProductID.ToString()).AppSettings(), out int totalTalkTime);
            v = int.TryParse(("LastTalkTime" + obj.ProductID.ToString()).AppSettings(), out int lastTalkTime);
            v = int.TryParse(("ProspectTalkTime" + obj.ProductID.ToString()).AppSettings(), out int prospectTalkTime);

            if (obj.Call.TotalTT > 0)
            {
                obj.LeadStatus.StatusID = 3;
            }
            if (obj.Call.TotalTT >= totalTalkTime || obj.Call.TotalTT >= lastTalkTime)
            {
                obj.LeadStatus.StatusID = 4;
            }
            if (prospectTalkTime > 0 && obj.Call.TotalTT >= prospectTalkTime)
            {
                obj.LeadStatus.StatusID = 11;
            }

            return obj.LeadStatus;
        }

        public static string GetCountryName(PriorityModel obj, long UserId)
        {
            obj.CountryName = null;
            try
            {
                if (obj.Country > 0)
                {
                    int countryId = obj.Country;
                    if (countryId.Equals(392) || countryId == 0)
                    {
                        obj.CountryName = "India";
                    }
                    else
                    {
                        var _masterData = MasterData.CountryMaster();
                        if (_masterData != null && _masterData.ContainsKey(countryId))
                            obj.CountryName = _masterData[countryId];
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), UserId, ex.ToString(), "GetCountryName", "CommAPI", "LeadprioritizationDLL", "", "", DateTime.Now, DateTime.Now);
            }

            return obj.CountryName;
        }

        public static OneLeadObj GetOneLeadObj(long UserId)
        {
            string Key = RedisCollection.OneLeadObj() + ":" + UserId;
            DateTime reqTime = DateTime.Now;
            OneLeadObj oneLeadObj = null;
            try
            {
                var obj = MatrixRedisHelper.GetRedisData(Key);
                if (obj != null)
                {
                    oneLeadObj = JsonConvert.DeserializeObject<OneLeadObj>(obj);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), UserId, ex.ToString(), "GetOneLeadObj", "CommAPI", "LeadprioritizationDLL", "", "", reqTime, DateTime.Now);
            }

            return oneLeadObj;
        }

        public static void MarkNoLeadObjInRedis(long UserId, OneLeadObj oneLeadObj)
        {
            DateTime reqTime = DateTime.Now;
            try
            {
                string Key = RedisCollection.OneLeadObj() + ":" + UserId;
                MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(oneLeadObj), new TimeSpan(7, 0, 0));
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), UserId, ex.ToString(), "MarkNoLeadObj", "CommAPI", "LeadprioritizationDLL", "", "", reqTime, DateTime.Now);
            }
        }

        public static CallingData GetCallableNumber(Int64 leadId, Int64 CustomerID)
        {

            CallingData obj = null;
            DateTime requestTime = DateTime.Now;
            string url = "coreAPIv1".AppSettings();
            string data = string.Empty;
            string exstr = string.Empty;
            try
            {

                string CustID = CryptoHelper.Encrytion_Payment_AES(CustomerID.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());
                if (!string.IsNullOrEmpty(CustID))
                {
                    url = url + "cs/mtx/getCallableNumber?customerId=" + CustID;
                    Dictionary<object, object> _Dict = new Dictionary<object, object>();

                    _Dict.Add("authKey", "coreAPIauthKey".AppSettings());
                    _Dict.Add("clientKey", "coreAPIclientKey".AppSettings());

                    int timeout = Convert.ToInt32("producttimeout".AppSettings());
                    data = CommonAPICall.CallAPI(url, "", "GET", timeout, "application/json", _Dict);
                    if (!string.IsNullOrEmpty(data))
                    {
                        obj = JsonConvert.DeserializeObject<CallingData>(data);
                    }
                }

            }
            catch (Exception ex)
            {
                exstr = ex.ToString();

            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, exstr, "GetCallableNumber", "OneLead", "LeadPrioritizationDLL", url, data, requestTime, DateTime.Now);
            }

            return obj;
        }

        public static bool IsCallAllowed_UnassignedUser(long UserId)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                string query = "SELECT 1 FROM CRM.UserGroupMaster(NOLOCK) UDM INNER JOIN CRM.UserGroupRoleMapNew(NOLOCK) UGR ON UDM.UserGroupID=UGR.GroupId WHERE (UDM.ProcessID=4 OR UGR.GroupID IN (467,1181,1287,1256,1471,1610)) AND UGR.UserId=@UserId";
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParam);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", UserId, ex.ToString(), "GetCallableNumber", "CoreApi", "LeadPrioritizationDLL", string.Empty, string.Empty, requestTime, DateTime.Now);
                return false;
            }

            return false;
        }

        public static bool SkipLead(long leadId, short SkipDuration)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = (Query<PriorityModel>.EQ(p => p.LeadID, leadId));
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.SkippingTime, DateTime.Now).Set(x => x.SkipDurationHrs, SkipDuration);
            _CommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
            return true;
        }

        public static bool LogLeadHistory(Int64 UserID, long LeadId, string Comments)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@UserID", UserID);
            sqlParam[1] = new SqlParameter("@LeadID", LeadId);
            sqlParam[2] = new SqlParameter("@EventType", 2);
            sqlParam[3] = new SqlParameter("@Comments", Comments);
            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveLeadHistory]", sqlParam);

            return true;
        }

        public static bool ChkBookedLeadinpriorityQueue(long LeadID, Int64 UserID, out short reasonId)
        {
            reasonId = 0;
            DateTime Requesttime = DateTime.Now;
            List<Int16> BookedSOSReasons = "BookedSOSReasons".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<string> BookedReasons = new() { "booked lead", "bookedlead" };
            try
            {
                var userdata = GetUserNxt5LeadsFromMongo(Convert.ToInt64(UserID));
                if (userdata != null && userdata.Leads != null)
                {
                    var data = userdata.Leads.Find(x => x.LeadId == LeadID);
                    if (userdata.Leads.Find(x => x.LeadId == LeadID && BookedReasons.Contains(x.Reason.ToLower())) != null)
                    {
                        reasonId = 30;
                        return true;
                    }
                    else if (data != null && BookedSOSReasons.Contains(data.ReasonId))
                    {
                        reasonId = userdata.Leads.Find(x => x.LeadId == LeadID).ReasonId;
                        return true;
                    }
                    else if (data != null)
                        reasonId = userdata.Leads.Find(x => x.LeadId == LeadID).ReasonId;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "ChkBookedLeadinpriorityQueue", "CommAPI", "LeadPrioritizationDLL", "", "counter", Requesttime, DateTime.Now);
                return false;
            }
        }

        public static PriorityModel GetPriorityModelMongo(Int64 LeadId)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection()).ToList();
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                    return lstPriorityModel[0];
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.ToString(), "GetPriorityModelMongo_Error", "LeadPrioritizationDLL", "", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static void UpdateCallFlagInMongo(Int64 LeadID, Int64 uid, Int16 ReasonID)
        {
            try
            {
                UpdateBuilder<PriorityModel> update = null;
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, LeadID);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.Call);
                var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);

                if (_priorityModel != null)
                {

                    if (_priorityModel.Call == null)
                    {
                        update = Update<PriorityModel>
                                            .Set(p => p.Call.calltime, DateTime.Now)
                                            .Set(p => p.Call.Disposition, String.Empty)
                                            .Set(p => p.Call.Duration, 1)
                                            .Set(p => p.Call.TalkTime, 0)
                                            .Set(p => p.Call.TotalTT, 0)
                                            .Set(p => p.Call.CallAttempts, 0)
                                            .Set(p => p.Call.uid, uid)
                                            .Set(p => p.Call.CallType, "OB")
                                            .Set(p => p.Call.TodaysAttempt, 0)
                                            .Set(p => p.Call.TodaysNANCAttempt, 0)
                                            .Set(p => p.Call.TodayAnswered, 0)
                                            .Set(p => p.Call.IsProcessed, false)
                                            .Set(p => p.Call.LastNminuteNANCeAttempts, 0)
                                            .Set(p => p.Call.ReasonID, ReasonID);
                    }
                    else
                    {
                        update = Update<PriorityModel>
                                            .Set(p => p.Call.calltime, DateTime.Now)
                                            .Set(p => p.Call.Disposition, String.Empty)
                                            .Set(p => p.Call.Duration, _priorityModel.Call.Duration)
                                            .Set(p => p.Call.TalkTime, _priorityModel.Call.TalkTime)
                                            .Set(p => p.Call.TotalTT, _priorityModel.Call.TotalTT)
                                            .Set(p => p.Call.CallAttempts, _priorityModel.Call.CallAttempts)
                                            .Set(p => p.Call.uid, uid)
                                            .Set(p => p.Call.CallType, "OB")
                                            .Set(p => p.Call.TodaysAttempt, (_priorityModel.Call.calltime.Date != DateTime.Now.Date ? 0 : _priorityModel.Call.TodaysAttempt))
                                            .Set(p => p.Call.TodaysNANCAttempt, (_priorityModel.Call.calltime.Date != DateTime.Now.Date ? 0 : _priorityModel.Call.TodaysNANCAttempt))
                                            .Set(p => p.Call.TodayAnswered, (_priorityModel.Call.calltime.Date != DateTime.Now.Date ? 0 : _priorityModel.Call.TodayAnswered))
                                            .Set(p => p.Call.LastNminuteNANCeAttempts, (_priorityModel.Call.calltime.Date != DateTime.Now.Date ? 0 : _priorityModel.Call.LastNminuteNANCeAttempts))
                                            .Set(p => p.Call.IsProcessed, false)
                                            .Set(p => p.Call.ReasonID, ReasonID);
                    }

                    objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "UpdateCallFlagInMongo", "UpdateCallFlagInMongo", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
            }
        }

        public static void UpdateWorkDoneLog(long leadId, Int64 userId, short reasonId, string callId, byte actionId, short addToQueue = 0)
        {
            try
            {
                LeadPriorityLog oLeadPriorityLog = new LeadPriorityLog
                {
                    LeadID = leadId,
                    UserID = Convert.ToInt64(userId),
                    ActionId = actionId,
                    WorkDoneTs = DateTime.Now,
                    AppearedReasonId = Convert.ToByte(reasonId),
                    CallId = callId,
                    AddToQueue = addToQueue
                };
                InsertPriorityLogToSql(oLeadPriorityLog);
            }
            catch (Exception) { }
        }

        public static void InsertPriorityLogToSql(LeadPriorityLog oLeadPriorityLog)
        {
            SqlParameter[] sqlParam = null;

            sqlParam = new SqlParameter[11];
            sqlParam[0] = new SqlParameter("@LeadId", oLeadPriorityLog.LeadID);
            sqlParam[1] = new SqlParameter("@IsPriorityLead", oLeadPriorityLog.IsPriorityLead);
            sqlParam[2] = new SqlParameter("@AppearedReasonId", oLeadPriorityLog.AppearedReasonId);
            sqlParam[3] = new SqlParameter("@Position", oLeadPriorityLog.Position);
            sqlParam[4] = new SqlParameter("@UserID", oLeadPriorityLog.UserID);
            sqlParam[5] = new SqlParameter("@WorkDoneTs", oLeadPriorityLog.WorkDoneTs);
            sqlParam[6] = new SqlParameter("@ActionId", oLeadPriorityLog.ActionId);
            sqlParam[7] = new SqlParameter("@FlagPositioningLog", oLeadPriorityLog.FlagPositioningLog);
            sqlParam[8] = new SqlParameter("@LeadAppearedAt", oLeadPriorityLog.LeadAppearedAt);
            sqlParam[9] = new SqlParameter("@CallId", oLeadPriorityLog.CallId);
            sqlParam[10] = new SqlParameter("@AddToQueue", oLeadPriorityLog.AddToQueue);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertLeadPriorityLog_LP]", sqlParam);
        }

        public static void UpdateCalldata(DialerDispDetails _DispositionUpdate)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                string strQuery = "UPDATE MTX.CallDataHistory SET Duration=@duration,talktime=@talktime,Disposition=@Disposition,CallID=@CallID, CallType='OB', UpdatedOn=GETDATE() WHERE CallDataID=@UID";
                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@Disposition", _DispositionUpdate.Disposition);
                SqlParam[1] = new SqlParameter("@CallID", _DispositionUpdate.CallId);
                SqlParam[2] = new SqlParameter("@UID", _DispositionUpdate.CallTrackingID);
                SqlParam[3] = new SqlParameter("@duration", _DispositionUpdate.Duration);
                SqlParam[4] = new SqlParameter("@talktime", _DispositionUpdate.talktime);
                object obj = SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", _DispositionUpdate.LeadID, ex.ToString(), "UpdateCalldata", "UpdateCalldata", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
            }
        }

        public static bool UpdateCallStatusOnCallInitiate(long LeadID, string Status, Int64 userId, Int64 CallDataID = 0, Int64 MobileNo = 0)
        {
            bool resp = true;
            DateTime requestTime = DateTime.Now;
            try
            {
                string Key = RedisCollection.Next5Leads() + ":" + userId;
                var obj = MatrixRedisHelper.GetRedisData(Key);
                if (obj != null)
                {
                    UserNext5Leads userdata = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
                    if (userdata.Leads != null && userdata.Leads.Count > 0)
                    {
                        var updateLead = userdata.Leads.Find(x => x.LeadId == LeadID);
                        if (updateLead != null)
                        {
                            updateLead.CallStatus = Status;

                            if (CallDataID > 0 && MobileNo > 0)
                            {
                                updateLead.CallDataID = CallDataID;
                                updateLead.MobileNo = MobileNo;
                            }
                            MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(userdata), new TimeSpan(7, 0, 0));
                        }
                    }
                }
                else
                {
                    //AddLeadToPriorityQueue
                }
            }
            catch (Exception ex)
            {
                resp = false;
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "UpdateCallStatusOnCallInitiate", "UpdateCallStatusOnCallInitiate", "LeadPrioritizationDLL", "", "", requestTime, DateTime.Now);
            }

            return resp;
        }

        public static CallData SetAnsWeekAttempts(PriorityModel _priorityModel)
        {
            CallData _CallData = new CallData();
            bool isRenewalGroup = false;
            List<Int16> healthProducts = new List<short>() { 2, 106, 118, 130 };

            try
            {
                if (healthProducts.Contains(_priorityModel.ProductID) || _priorityModel.ProductID == 117 || _priorityModel.ProductID == 131)
                    isRenewalGroup = IsRenewalGroup(_priorityModel.User.UserID);

                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                var _ProductWiseCOnstant = LeadPrioritizationDLL.getPriorityConfigByProduct(_priorityModel.ProductID, isRenewalGroup);

                Int16 current_week = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(DateTime.Now.Date.Subtract(_priorityModel.User.FirstAssignedOn.Date).TotalDays) / 7) + 1);
                _CallData.Current_Week = current_week;

                if (current_week == 1 || _priorityModel.Call.Current_Week == current_week)
                {
                    Int16 MaxWeekAttempt = current_week == 1 ? _ProductWiseCOnstant.Week1MaxAttempts : _ProductWiseCOnstant.WeekMaxAttempts;

                    if (_priorityModel.Call.Week_Attempt > (MaxWeekAttempt - _ProductWiseCOnstant.AnsWeekAttempts))
                        _CallData.Week_Attempt = Convert.ToInt16(MaxWeekAttempt - _ProductWiseCOnstant.AnsWeekAttempts);

                    else
                        _CallData.Week_Attempt = _priorityModel.Call.Week_Attempt;

                }
                else
                {
                    _CallData.Week_Attempt = 0;
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(_priorityModel.LeadID), 0, ex.ToString(), "SetAnsWeekAttempts", "OneLeadPriority", "LeadPrioritizationDLL", JsonConvert.SerializeObject(_priorityModel), string.Empty, DateTime.Now, DateTime.Now);
            }

            return _CallData;
        }

        public static bool IsRenewalGroup(long UserId)
        {
            bool renewalGroup = false;
            try
            {
                short processId = 0;
                OneLeadObj oneLeadObj = LeadPrioritizationDLL.GetOneLeadObj(UserId);
                if (oneLeadObj != null)
                {
                    processId = oneLeadObj.ProcessId;
                }
                else
                {
                    var ds = LeadPrioritizationDLL.GetGroupNameandID(UserId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        processId = (ds.Tables[0].Rows[0]["ProcessID"] == null || ds.Tables[0].Rows[0]["ProcessID"] == DBNull.Value) ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["ProcessID"]);
                    }
                }

                if (processId == 4)
                    renewalGroup = true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, UserId, ex.ToString(), "IsRenewalGroup", "OneLeadPriority", "LeadPrioritizationDLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            return renewalGroup;
        }

        public static productPriorityconstant getPriorityConfigByProduct(Int16 productID, bool renewal = false, Int16 GroupID = 0)
        {
            List<Int16> LeadPriorityProduct = "LeadPriorityProduct".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> PaymentGroups = "HealthPaymentGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> ChatRenewalGroups = "MotorChatGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> ChatIBRenewalGroups = "ChatIBRenewalGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> HealthFosGroups = "HealthFOSGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();

            List<Int16> RenwalProducts = new List<short>() { 2, 117, 131, 101, 139 };
            if (productID == 106 || productID == 118 || productID == 130)
                productID = 2;

            else if (!LeadPriorityProduct.Contains(productID))
            {
                productID = 117;
            }


            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            if (RenwalProducts.Contains(productID) && PaymentGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "PAYMENT").SingleOrDefault();
            else if (RenwalProducts.Contains(productID) && HealthFosGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "FOS").SingleOrDefault();
            else if (productID == 117 && ChatRenewalGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "CHATRENEWAL").SingleOrDefault();
            else if (productID == 117 && ChatIBRenewalGroups.Contains(GroupID))
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "CHATIBRENEWAL").SingleOrDefault();
            else if (RenwalProducts.Contains(productID) && renewal)
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct.ToUpper() == "RENEWAL").SingleOrDefault();
            else
                return _LeadPriorityConstants.PriorityConstant.Where(p => p.productID == productID && p.subProduct == "").SingleOrDefault();
        }

        public static void InsertPriorityModelInMongo(PriorityModel oPriorityModel)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            objCommDB.InsertData(oPriorityModel, DataAccessLibrary.MongoCollection.LPDataCollection());
            oPriorityModel.MongoOperation = true;
        }

        public static void InsUpLeadPointsInSQL(LeadPointsDTO oLeadPointsDTO)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[8];
                sqlParam[0] = new SqlParameter("@LeadId", oLeadPointsDTO.LeadId);
                sqlParam[1] = new SqlParameter("@Points", oLeadPointsDTO.Points);
                sqlParam[2] = new SqlParameter("@PointType", oLeadPointsDTO.PointType);
                sqlParam[3] = new SqlParameter("@PointSubType", oLeadPointsDTO.PointSubType);
                sqlParam[4] = new SqlParameter("@IsPaymentCB ", oLeadPointsDTO.IsPaymentCB);
                sqlParam[5] = new SqlParameter("@FirstAssignedOn", oLeadPointsDTO.FirstAssignedOn);
                sqlParam[6] = new SqlParameter("@WeekPoints", oLeadPointsDTO.WeekPoints);
                sqlParam[7] = new SqlParameter("@CurrentWeek", oLeadPointsDTO.CurrentWeek);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLeadCreditsPoint_LP]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", oLeadPointsDTO.LeadId, ex.ToString(), "InsUpLeadPointsInSQL_Error", "OneLeadPriority", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
            }

        }

        public static List<PriorityModel> GetAgentPriorityLeads_Customer(Int64 CustID, Int64 UserID)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query.And(
                                Query<PriorityModel>.EQ(p => p.CustID, CustID),
                                Query<PriorityModel>.EQ(p => p.IsActive, true),
                                Query<PriorityModel>.EQ(p => p.User.UserID, UserID)
                                );
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection()).ToList();
                return lstPriorityModel;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustID.ToString(), CustID, ex.ToString(), "GetPriorityLeads_Customer", "CommAPI", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static List<PriorityModel> GetPriorityLeads_Customer(Int64 CustID)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoSortBy _Sort = SortBy.Descending("_id");
                var query = Query.And(Query<PriorityModel>.EQ(p => p.CustID, CustID), Query<PriorityModel>.EQ(p => p.IsActive, true));
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), _Sort, 25).ToList();
                return lstPriorityModel;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustID.ToString(), CustID, ex.ToString(), "GetPriorityLeads_Customer", "CommAPI", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        //This method will update Status Data in Mongo
        public static void UpdateLeadStatusData(ref UpdateBuilder<PriorityModel> update, PriorityModel respPriorityModel, PriorityModel reqPriorityModel)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var activeleadset = respPriorityModel.ActiveLeadSet;
            bool ReOpenFlag = false;
            bool isBooked = false;
            if (!_LeadPriorityConstants.MatrixStatus.Contains(reqPriorityModel.LeadStatus.StatusID))//rejected leads
            {

                if (_LeadPriorityConstants.MatrixBookedStatus.Contains(reqPriorityModel.LeadStatus.StatusID))
                {
                    isBooked = true;
                    update = Update<PriorityModel>.Set(p => p.LeadStatus.StatusID, reqPriorityModel.LeadStatus.StatusID)
                                   .Set(p => p.LeadStatus.SubStatusID, reqPriorityModel.LeadStatus.SubStatusID)
                                   .Set(p => p.LeadStatus.Statustime, reqPriorityModel.LeadStatus.Statustime)
                                   .Set(p => p.IsActive, true)
                                   .Set(p => p.IsBooked, true);
                }
                else if (_LeadPriorityConstants.PolicyIssuedStatus.Contains(reqPriorityModel.LeadStatus.StatusID)) //policyissued status
                {
                    isBooked = true;
                    update = Update<PriorityModel>.Set(p => p.IsBooked, false);
                }

                // if lead is rejected or in booked status remove that lead from the active set
                if (activeleadset != null)
                {
                    if (update == null)
                    {
                        update = new UpdateBuilder<PriorityModel>();
                    }
                    activeleadset.Remove(reqPriorityModel.LeadStatus.Leads[0]);
                    update = update.Pull(p => p.ActiveLeadSet, reqPriorityModel.LeadStatus.Leads[0]);
                    if (activeleadset.Count == 0 && respPriorityModel.IsBooked == false && !isBooked)
                        update = update.Set(p => p.IsActive, false);
                }
            }
            else
            {
                // if lead is not exist in the active list then add the same                
                if (activeleadset != null && !activeleadset.Contains(reqPriorityModel.LeadStatus.Leads[0]))
                {
                    if (activeleadset.Count == 0)// means active set was all rejected.
                        ReOpenFlag = true;

                    activeleadset.Add(reqPriorityModel.LeadStatus.Leads[0]);
                    update = Update<PriorityModel>.Set(p => p.ActiveLeadSet, activeleadset);
                    if (ReOpenFlag)
                        update = update.Set(p => p.IsActive, true);
                }
                if (respPriorityModel.LeadStatus != null && reqPriorityModel.LeadStatus.StatusID > respPriorityModel.LeadStatus.StatusID)//Updating with highest status on set
                {
                    if (update == null)
                    {
                        update = new UpdateBuilder<PriorityModel>();
                    }

                    update = update.Set(p => p.LeadStatus.StatusID, reqPriorityModel.LeadStatus.StatusID)
                                   .Set(p => p.LeadStatus.SubStatusID, reqPriorityModel.LeadStatus.SubStatusID)
                                   .Set(p => p.LeadStatus.Statustime, reqPriorityModel.LeadStatus.Statustime);
                }
            }
        }

        public static void InsertUpdateRevisitPageInfo(string Page, long LeadId, Int64 UserID, bool IsTimeUpdate, string roomcode, long visitLeadID, long visitEnquiryID, DateTime Revisittime)
        {

            DateTime requestTime = DateTime.Now;
            string RequestText = string.Empty;
            RequestText = "Page: " + Page + " ,ParentLeadID: " + LeadId.ToString();
            OnlineCustomerInfo oOnlineCustomerInfo = new OnlineCustomerInfo();
            PriorityModel ResponsePriorityModel = new PriorityModel();
            string exception = string.Empty;
            try
            {

                ResponsePriorityModel = GetPriorityModelMongo(LeadId);// getting 
                if (ResponsePriorityModel != null && ResponsePriorityModel.IsActive == false)
                {

                    RequestText = RequestText + ResponsePriorityModel == null ? ", ResponsePriorityModel= null " : " , Isactive= false";
                    return;
                }

                RequestText = RequestText + " , Insertion Since: lstlead= null";
                oOnlineCustomerInfo.LeadId = LeadId;
                oOnlineCustomerInfo.CustName = ResponsePriorityModel == null ? "" : ResponsePriorityModel.CustName;// lead not assigned also handled
                oOnlineCustomerInfo.Page = Page;
                oOnlineCustomerInfo.ts = Revisittime;
                oOnlineCustomerInfo.AgentId = ResponsePriorityModel == null ? UserID : ResponsePriorityModel.User.UserID;// lead not assigned also handled
                oOnlineCustomerInfo.visitEnquiryID = visitEnquiryID;
                oOnlineCustomerInfo.visitLead = visitLeadID;
                if (!string.IsNullOrEmpty(roomcode))
                    oOnlineCustomerInfo.RoomCode = roomcode;

                string Key = RedisCollection.RevisitCustomers() + ":" + LeadId;
                MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(oOnlineCustomerInfo), new TimeSpan(7, 0, 0));

                if (visitEnquiryID > 0)
                {
                    Key = RedisCollection.RevisitEnquiry() + ":" + visitEnquiryID;
                    MatrixRedisHelper.SetRedisData(Key, Convert.ToString(LeadId), new TimeSpan(7, 0, 0));
                }
                else if (visitLeadID > 0)
                {
                    Key = RedisCollection.VisitLeadId() + ":" + visitLeadID;
                    MatrixRedisHelper.SetRedisData(Key, Convert.ToString(LeadId), new TimeSpan(7, 0, 0));
                }

                //string url = "customerrevisit".AppSettings() + "customerrevisit/";
                //CommonAPICall.PostAPICall_Sync(url, 1000, Newtonsoft.Json.JsonConvert.SerializeObject(oOnlineCustomerInfo), "");

                //update data in redis
                Key = RedisCollection.RedisKey() + ":" + LeadId;
                MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(oOnlineCustomerInfo), new TimeSpan(12, 0, 0));

                //add data in hash
                if (oOnlineCustomerInfo.AgentId > 0)
                {
                    Key = RedisCollection.LeadAgentIndex() + ":" + oOnlineCustomerInfo.AgentId;
                    MatrixRedisHelper.SetZAdd(Key, Convert.ToString(LeadId), new TimeSpan(12, 0, 0));
                }
            }

            catch (Exception ex)
            {
                exception = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, exception, "InsertRevisitPageInfo_error", "InsertRevisitPageInfo_error", "LeadPrioritizationDLL", RequestText, "", requestTime, DateTime.Now);
            }

            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, exception, "InsertRevisitPageInfo", "InsertRevisitPageInfo", "LeadPrioritizationDLL", RequestText, "", requestTime, DateTime.Now);
            }
        }

        public static void UpdateQuoteShared(long LeadID, Int64 AgentId, string CustomerName)
        {
            DateTime requestTime = DateTime.Now;
            string RequestText = string.Empty;
            QuoteSharedTracking _QuoteSharedTracking = new QuoteSharedTracking();
            string exception = string.Empty;
            try
            {
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                _QuoteSharedTracking.LeadId = LeadID;
                _QuoteSharedTracking.ts = DateTime.Now;
                _QuoteSharedTracking.AgentId = AgentId;
                _QuoteSharedTracking.CustName = CustomerName;
                objCommDB.InsertData(_QuoteSharedTracking, DataAccessLibrary.MongoCollection.QuoteSharedTracking());
            }

            catch (Exception ex)
            {
                exception = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, exception, "UpdateQuoteShared", "UpdateQuoteShared", "UpdateQuoteShared", RequestText, "", requestTime, DateTime.Now);
            }
        }

        public static CallData SetWeekAttempts(PriorityModel _priorityModel)
        {
            CallData _CallData = new CallData();
            bool isRenewalGroup = false;
            List<Int16> healthProducts = new List<short>() { 2, 106, 118, 130 };
            List<Int16> RestrictCalling = "RestrictCallingProducts".AppSettings().Split(',').Select(Int16.Parse).ToList();

            try
            {
                if (healthProducts.Contains(_priorityModel.ProductID) || _priorityModel.ProductID == 117 || _priorityModel.ProductID == 131)
                    isRenewalGroup = LeadPrioritizationDLL.IsRenewalGroup(_priorityModel.User.UserID);

                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                var _ProductWiseCOnstant = LeadPrioritizationDLL.getPriorityConfigByProduct(_priorityModel.ProductID, isRenewalGroup);

                Int16 current_week = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(DateTime.Now.Date.Subtract(_priorityModel.User.FirstAssignedOn.Date).TotalDays) / 7) + 1);
                if (RestrictCalling.Contains(_priorityModel.ProductID))
                    _CallData.TodaysNANCAttempt = (_priorityModel.Call.calltime.Date != DateTime.Now.Date) ? Convert.ToInt16(0) : Convert.ToInt16(_priorityModel.Call.TodaysNANCAttempt > 3 && (_priorityModel.Revisit == null || _priorityModel.Revisit.ts.Date != DateTime.Now.Date || _priorityModel.CustomerTodayNANC == 4) ? 3 : _priorityModel.Call.TodaysNANCAttempt);
                else
                    _CallData.TodaysNANCAttempt = (_priorityModel.Call.calltime.Date != DateTime.Now.Date) ? Convert.ToInt16(0) : Convert.ToInt16(_priorityModel.Call.TodaysNANCAttempt > 3 ? 2 : _priorityModel.Call.TodaysNANCAttempt);
                _CallData.Current_Week = current_week;
                _CallData.Week_Attempt = current_week == 1 ? (_priorityModel.Call.Week_Attempt > (_ProductWiseCOnstant.Week1MaxAttempts - 3) ? Convert.ToInt16(_ProductWiseCOnstant.Week1MaxAttempts - 3) : _priorityModel.Call.Week_Attempt) : (_priorityModel.Call.Week_Attempt > (_ProductWiseCOnstant.WeekMaxAttempts - 3) ? Convert.ToInt16(_ProductWiseCOnstant.WeekMaxAttempts - 3) : _priorityModel.Call.Week_Attempt);
                //_CallData.CustomerNANCAttempt = _CallData.TodaysNANCAttempt;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(_priorityModel.LeadID), 0, ex.ToString(), "SetWeekAttempts", "LeadPrioritizationDLL", "Communication", JsonConvert.SerializeObject(_priorityModel), string.Empty, DateTime.Now, DateTime.Now);
            }
            return _CallData;
        }

        public static DataSet GetAgentDetails(string UserId)
        {
            DateTime reqTime = DateTime.Now;
            string connection = ConnectionClass.LivesqlConnection();
            SqlParameter[] _Param = new SqlParameter[1];
            _Param[0] = new SqlParameter("@AgentCode", UserId);
            return SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, "MTX.GetAgentDetails", _Param);
        }

        public static string[] getLogoutReasons(string Collection)
        {
            string[] sysConfigs = { };
            StringBuilder sb = new();
            var list = new List<string>();


            try
            {
                if (MemoryCache.Default[Collection] != null)
                    sysConfigs = (string[])MemoryCache.Default.Get(Collection);

                else
                {
                    DateTime reqTime = DateTime.Now;
                    string connection = ConnectionClass.ReplicasqlConnection();

                    SqlParameter[] _Param = new SqlParameter[1];
                    _Param[0] = new SqlParameter("@MasterTypeId", 1);
                    DataSet oDataSet = SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, "[MTX].[GetMasterTypeList]", _Param);

                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            string val = row["Name"] == DBNull.Value ? string.Empty : Convert.ToString(row["Name"]).ToUpper();
                            list.Add(val.ToString());
                        }
                        sysConfigs = list.ToArray();
                        if (list.Count > 0)
                            CommonCache.GetOrInsertIntoCache(list.ToArray(), Collection, 8 * 60);
                    }
                }
            }
            catch (Exception)
            {
                return null;
            }
            return sysConfigs;
        }

        public static CallingNoModel getMobileDetails(Int64 leadId, string CustomerId)
        {
            CallingNoModel objgetCallingNoModel = null;
            DateTime dt = DateTime.Now;
            string url = "coreAPI".AppSettings();
            string data = string.Empty;
            string exstr = string.Empty;
            try
            {
                Guid obj = Guid.NewGuid();
                string CustId = CryptoHelper.Encrytion_Payment_AES(CustomerId.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());
                url = "coreAPIv1".AppSettings();
                // url = url + "cs/mtx/getMobileDetails?customerId=" + CustId;
                url = url + "cs/mtx/getMobileDetails?customerId=" + CustId + "&trackingId=" + obj.ToString();


                //Dictionary<string, string> _Dict = new Dictionary<string, string>();
                Dictionary<object, object> _Dict = new Dictionary<object, object>();
                _Dict.Add("authKey", "coreAPIauthKey".AppSettings());
                _Dict.Add("clientKey", "coreAPIclientKey".AppSettings());
                int timeout = 2000; //Convert.ToInt32("DialerAPITimeout".AppSettings());

                if (!string.IsNullOrEmpty(url))
                    //data = CommonAPICall.getAPICallWithResult(url, timeout, _Dict);
                    data = CommonAPICall.CallAPI_New(url, "", "GET", timeout, "application/json", _Dict);
                if (!string.IsNullOrEmpty(data))
                {
                    objgetCallingNoModel = JsonConvert.DeserializeObject<CallingNoModel>(data.ToString());
                }

            }
            catch (Exception ex)
            {
                exstr = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, exstr, "getMobileDetails", "getMobileDetails", "LeadPrioritizationDLL", url, data, dt, DateTime.Now);

            }

            return objgetCallingNoModel;
        }

        public static DataSet GetCallingNumberData(string CustId, string LeadId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@CustomerId", Convert.ToInt64(CustId));
                sqlParam[1] = new SqlParameter("@leadId", Convert.ToInt64(LeadId));
                ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCallingNumberData]", sqlParam);
            }
            catch (Exception)
            {
                return null;
            }

            return ds;
        }
        public static PriorityModel GetLeadPriorityModel(Int64 LeadId)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = Query.And(Query<PriorityModel>.EQ(p => p.LeadID, LeadId));
            return _CommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection());

        }
        public static DataSet GetLeaddetailsByCallIDData(long leadID, string callID)
        {

            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@CallID", callID);
            SqlParam[1] = new SqlParameter("@LeadID", leadID);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeaddetailsByCallID]", SqlParam);

        }
        public static string InsertCallData(DialerDispDetails _DispositionUpdate)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlParam = new SqlParameter[22];
            sqlParam[0] = new SqlParameter("@CallID", _DispositionUpdate.CallId);
            if (_DispositionUpdate.IsService.Equals(1))
            {
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.LeadID);
            }
            else
            {
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.ParentID);
            }

            sqlParam[2] = new SqlParameter("@EmployeeID", _DispositionUpdate.AgentCode);
            sqlParam[3] = new SqlParameter("@CallDate", _DispositionUpdate.callDate);
            sqlParam[4] = new SqlParameter("@Duration", _DispositionUpdate.Duration);
            sqlParam[5] = new SqlParameter("@talktime", _DispositionUpdate.talktime);
            sqlParam[6] = new SqlParameter("@CallType", _DispositionUpdate.CallType);
            sqlParam[7] = new SqlParameter("@Status", _DispositionUpdate.Status);
            sqlParam[8] = new SqlParameter("@productId", _DispositionUpdate.ProductID);
            if (!string.IsNullOrEmpty(_DispositionUpdate.Disposition))
            {
                sqlParam[9] = new SqlParameter("@disposition", _DispositionUpdate.Disposition);
            }
            sqlParam[10] = new SqlParameter("@Context", _DispositionUpdate.Context);
            sqlParam[11] = new SqlParameter("@IsBMS", _DispositionUpdate.IsBMS);
            if (!string.IsNullOrEmpty(_DispositionUpdate.CallTrackingID))
            {
                sqlParam[12] = new SqlParameter("@CallDataID", Convert.ToInt64(_DispositionUpdate.CallTrackingID));
            }
            else
            {
                sqlParam[12] = new SqlParameter("@CallDataID", 0);
            }
            sqlParam[12].Direction = ParameterDirection.InputOutput;
            sqlParam[13] = new SqlParameter("@countryCode", _DispositionUpdate.CountryCode);
            sqlParam[14] = new SqlParameter("@phone", _DispositionUpdate.dst);
            sqlParam[15] = new SqlParameter("@IP", _DispositionUpdate.AsteriskIP);
            sqlParam[16] = new SqlParameter("@C2CID", _DispositionUpdate.C2CID);

            //InsertCheckFlag Added by sundarthapa 21-02-2019
            sqlParam[17] = new SqlParameter("@InsertCompleted", _DispositionUpdate.InsertCheckFlag);
            sqlParam[17].Direction = ParameterDirection.InputOutput;
            sqlParam[18] = new SqlParameter("@t_type", _DispositionUpdate.t_type);
            sqlParam[19] = new SqlParameter("@RecFileName", _DispositionUpdate.recfile);
            sqlParam[20] = new SqlParameter("@CallingNo", _DispositionUpdate.CallingNo);
            sqlParam[21] = new SqlParameter("@IsFailover", _DispositionUpdate.IsFailover);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertCallData]", sqlParam);

            _DispositionUpdate.InsertCheckFlag = Convert.ToByte(sqlParam[17].Value);
            return Convert.ToString(sqlParam[12].Value);

        }
        public static void SaveAppHistory(DialerDispDetails dialerDispDetails)
        {
            DateTime dt = DateTime.Now;
            try
            {
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, dialerDispDetails.LeadID);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.Appointment);

                var priority = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);
                if (priority != null && (priority.Call == null || priority.Call.IsProcessed == false) && priority.Appointment != null && priority.Appointment.AppointmentId > 0)
                    SaveAppointmentHistory(priority, dialerDispDetails);


            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, dialerDispDetails.LeadID, ex.ToString(), "SaveAppHistory", "LeadPrioritizationDLL", "OneLeadPriority", dialerDispDetails.LeadID.ToString(), "", dt, DateTime.Now);
            }
        }
        public static void SaveAppointmentHistory(PriorityModel priorityModel, DialerDispDetails dialerDispDetails)
        {
            DateTime dt = DateTime.Now;

            SqlParameter[] SqlParam = new SqlParameter[6];
            SqlParam[0] = new SqlParameter("@AppointmentID", priorityModel.Appointment.AppointmentId);
            SqlParam[1] = new SqlParameter("@SubstatusId", priorityModel.Appointment.StatusID);
            SqlParam[2] = new SqlParameter("@EventType", EnumAppEvents.Calling);
            SqlParam[3] = new SqlParameter("@AppDateTime", priorityModel.Appointment.ScheduledOn);
            SqlParam[4] = new SqlParameter("@EmployeeID", dialerDispDetails.AgentCode);
            SqlParam[5] = new SqlParameter("@CallTrackingID", dialerDispDetails.CallTrackingID);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SetAppLogsByCalling]", SqlParam);

        }
        public static DataSet GetLeadCallDetails(long leadID, bool Isreplica = false)
        {

            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@leadId", leadID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadCallDetails]", SqlParam);

        }
        public static int UpdateLeadStatusByAutomation(long leadID, int statusID, int SubStatusId, Int64 userID)
        {
            string strexception = String.Empty;
            string strrequest = string.Empty;
            DateTime requesttime = DateTime.Now;

            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@LeadID", leadID);
            sqlParam[1] = new SqlParameter("@StatusID", statusID);
            sqlParam[2] = new SqlParameter("@SubStatusId", SubStatusId);
            sqlParam[3] = new SqlParameter("@UserID", userID);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLeadStatusByAutomation]", sqlParam);
            return 1;


        }
        public static int GetLeadCreditPoint(long LeadID)
        {
            int points = 0;

            string strQuery = "MTX.GetCreditPointDetails";
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", LeadID);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, strQuery, SqlParam);
            if (ds != null)
            {
                if (ds.Tables[0].Rows.Count > 0)
                {

                    points = Convert.ToInt32(ds.Tables[0].Rows[0]["Points"]);
                }
            }

            return points;
        }
        public static bool UpdateLeadCreditPoints(Int64 userId, long leadId, int AttemptID)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            try
            {
                sqlParam[0] = new SqlParameter("@AgentID", userId);
                sqlParam[1] = new SqlParameter("@LeadID", leadId);
                sqlParam[2] = new SqlParameter("@AttemptID", AttemptID);
                int ret = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLeadCreditPoints]", sqlParam);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        public static PriorityModel GetLeadDetails(long LeadId)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

                var query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.LeadID, p => p.ProductID,
                    p => p.Country, p => p.LeadPoints, p => p.Call, p => p.CustomerNANCAttempt,
                    p => p.CustomerTodayNANC, p => p.CustomerCalltime, p => p.User, p => p.CallBack, p => p.Appointment, p => p.DNC, p => p.Income, p => p.LeadSource);
                oPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, fields, DataAccessLibrary.MongoCollection.LPDataCollection()).ToList();

                if (oPriorityModel != null && oPriorityModel.Count > 0)
                    return oPriorityModel[0];
                else return null;

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.ToString(), "GetCountryIdOfLeads", "LeadPrioritizationDLL", "OneLead", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static bool IsHealthRenewalGrp(Int64 UserId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsHealthRenewalGrp]", sqlParam);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    return true;
            }
            catch (Exception ex)
            {
                return false;
            }

            return false;
        }

        public static Byte GetRestrictLeadAttempts(Int64 LeadID)
        {
            Byte RestrictedLeadAttempts = 0;
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@LeadID", LeadID);

                var obj = SqlHelper.ExecuteScalar(Connectionstring, CommandType.StoredProcedure, "[MTX].[GetRestrictLeadAttemptsV2]", SqlParam);

                if (obj != null)
                    RestrictedLeadAttempts = Convert.ToByte(obj);
            }
            catch (Exception ex)
            {

            }

            return RestrictedLeadAttempts;
        }

        public static DataSet getParentID(Int64 LeadID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadID", LeadID);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[getParentID]", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public static bool IsLeadCallableByPolicyStatus(long leadId)
        {
            var response = true;
            try
            {
                var sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", leadId);

                var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                     CommandType.StoredProcedure,
                                                     "[MTX].[GetLeadCallStatusByEnforcement]",
                                                     sqlParam);
                if (result != null)
                {
                    response = Convert.ToBoolean(result);
                }
            }
            catch (Exception ex)
            {

            }
            return response;
        }
        public static bool AddLeadToPriorityQueue(UserNext5Leads priorityLead)
        {
            try
            {
                UserNext5Leads userdata = new UserNext5Leads();

                priorityLead.Leads[0].ts = DateTime.Now;
                if (priorityLead.Leads[0].ReasonId != 33)
                    priorityLead.Leads[0].Priority = 1;

                string Key = RedisCollection.Next5Leads() + ":" + priorityLead.UserId;
                string obj = MatrixRedisHelper.GetRedisData(Key);

                if (obj == null)
                {
                    MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(priorityLead), new TimeSpan(7, 0, 0));
                }
                else
                {
                    userdata = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
                    if (userdata.Leads == null)
                        userdata.Leads = new List<Next5WidgetLead>();

                    if (userdata.Leads.Find(x => x.LeadId == priorityLead.Leads[0].LeadId) == null)
                        userdata.Leads.Add(priorityLead.Leads[0]);
                    else
                    {
                        var updateLead = userdata.Leads.Find(x => x.LeadId == priorityLead.Leads[0].LeadId);
                        updateLead.Priority = priorityLead.Leads[0].Priority;
                        updateLead.CallStatus = priorityLead.Leads[0].CallStatus;
                        updateLead.ReasonId = priorityLead.Leads[0].ReasonId;
                        updateLead.Reason = priorityLead.Leads[0].Reason;
                    }

                    MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(userdata), new TimeSpan(7, 0, 0));
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "AddLeadToPriorityQueue", "AddLeadToPriorityQueue", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
                return false;
            }

            return true;
        }
        public static int GetReleaseLeadsCount(Int64 UserID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserID", UserID);

            var count = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetReleaseLeadsCount]", sqlParam);

            return Convert.ToInt32(count);
        }

        public static bool INSERTLeadReopenTrack(long LeadId, int SubStatusID, Int64 UserID)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                string strQuery = string.Empty;
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);
                SqlParam[1] = new SqlParameter("@UserID", UserID);
                SqlParam[2] = new SqlParameter("@SubStatusID", SubStatusID);
                SqlHelper.ExecuteNonQuery(Connectionstring, CommandType.StoredProcedure, "[MTX].[INSERTLeadReopenTrack]", SqlParam);


                // update attempts in mongo
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.Call, p => p.User, p => p.ProductID);

                var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);
                if (_priorityModel != null)
                {
                    CallData _CallData = LeadPrioritizationDLL.SetWeekAttempts(_priorityModel);
                    UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(p => p.Call.Current_Week, _CallData.Current_Week)
                                                                                .Set(p => p.Call.Week_Attempt, _CallData.Week_Attempt)
                                                                                .Set(p => p.Call.TodaysNANCAttempt, _CallData.TodaysNANCAttempt)
                                                                                .Set(p => p.CustomerNANCAttempt, _CallData.TodaysNANCAttempt);
                    objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public static void ReleaseRequest(Int64 UserID, Int64 LeadID, Int16 Status)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@UserID", UserID);
            sqlParam[1] = new SqlParameter("@LeadID", LeadID);
            sqlParam[2] = new SqlParameter("@Status", Status);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateReleaseRequest_LP]", sqlParam);

        }
        public static List<long> GetActiveSetMongo(long LeadId)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            List<long> leads = null;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = (Query<PriorityModel>.EQ(p => p.LeadID, LeadId));
                leads = (List<long>)_CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection()).Select(y => y.ActiveLeadSet).SingleOrDefault();// leads having atleast 1 lead in their active set and point greater than 0

            }
            catch (Exception ex)
            {

            }
            return leads;
        }
        public static void UpdateCallReleaseRequestHistory(long LeadID, Int64 UserID)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            sqlParam[0].SqlDbType = SqlDbType.BigInt;
            sqlParam[1] = new SqlParameter("@UserID", UserID);
            sqlParam[1].SqlDbType = SqlDbType.BigInt;
            string strQuery = "INSERT INTO [MTX].[CallReleaseRequest_History] (LeadID,UserID,ts) values(@LeadId,@UserID,getdate())";
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.Text, strQuery, sqlParam);
        }
        public static bool RejectMatrixLeads(string leads, Int64 CreatedBy)
        {

            SqlParameter[] param2 = new SqlParameter[7];
            param2[0] = new SqlParameter("@Leads", leads);
            param2[1] = new SqlParameter("@ProductId", 0);
            param2[2] = new SqlParameter("@SubStatusId", 0);
            param2[3] = new SqlParameter("@UserID", CreatedBy);
            param2[4] = new SqlParameter("@FutureProsDate", null);
            param2[5] = new SqlParameter("@Url", "");
            param2[6] = new SqlParameter("@Result", 0);
            param2[6].Direction = ParameterDirection.Output;
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[RejectLeads_CV]", param2);
            return true;
        }
        public static UnAnsweredSummary GetUnAnsweredSummary(Int64 LeadID, Int16 ProductID)
        {
            UnAnsweredSummary _UnAnsweredSummary = new UnAnsweredSummary();
            try
            {
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                bool isRenewalGroup = false;
                List<Int16> healthProducts = new List<short>() { 2, 106, 118, 130 };

                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, LeadID);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.Call, p => p.User);

                var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);
                if (_priorityModel == null) return _UnAnsweredSummary;

                if (healthProducts.Contains(ProductID) || ProductID == 117 || ProductID == 131)
                    isRenewalGroup = LeadPrioritizationDLL.IsHealthRenewalGrp(_priorityModel.User.UserID);

                var _ProductLeadPriorityConstants = getPriorityConfigByProduct(ProductID, isRenewalGroup);
                short Week1MaxAttempts = _ProductLeadPriorityConstants.Week1MaxAttempts;
                short WeekMaxAttempts = _ProductLeadPriorityConstants.WeekMaxAttempts;
                short MaxAttempts = _ProductLeadPriorityConstants.MaxAttempts;

                Int16 currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(DateTime.Now.Date.Subtract(_priorityModel.User.FirstAssignedOn.Date).TotalDays) / 7) + 1);

                if (_priorityModel.Call == null)
                {
                    _UnAnsweredSummary.WeekAttempts = currentWeek == 1 ? Week1MaxAttempts : WeekMaxAttempts;
                    _UnAnsweredSummary.TodayNANC = 4;
                    _UnAnsweredSummary.CurrentWeek = currentWeek;
                    _UnAnsweredSummary.AttemptsTillDate = _priorityModel.User.FirstAssignedOn.AddDays(currentWeek * 7);
                    _UnAnsweredSummary.MaxAttempts = MaxAttempts;
                    _UnAnsweredSummary.CustomerNANC = 4;
                    _UnAnsweredSummary.CustomerTodayNANC = 0;
                }
                else
                {
                    _UnAnsweredSummary.WeekAttempts = (currentWeek == 1) ? (Week1MaxAttempts - _priorityModel.Call.Week_Attempt) : ((currentWeek != _priorityModel.Call.Current_Week) ? WeekMaxAttempts : WeekMaxAttempts - _priorityModel.Call.Week_Attempt);
                    _UnAnsweredSummary.TodayNANC = _UnAnsweredSummary.WeekAttempts == 0 ? 0 : (_priorityModel.Call.calltime.Date != DateTime.Now.Date) ? 4 : 4 - _priorityModel.Call.TodaysNANCAttempt;
                    _UnAnsweredSummary.CurrentWeek = currentWeek;
                    _UnAnsweredSummary.AttemptsTillDate = _priorityModel.User.FirstAssignedOn.AddDays(currentWeek * 7);
                    _UnAnsweredSummary.MaxAttempts = (MaxAttempts - _priorityModel.Call.NANC_Attempts) > 0 ? MaxAttempts - _priorityModel.Call.NANC_Attempts : 0;
                    _UnAnsweredSummary.CustomerNANC = _UnAnsweredSummary.WeekAttempts == 0 ? 0 : (_priorityModel.CustomerCalltime.Date != DateTime.Now.Date) ? 4 : 4 - _priorityModel.CustomerNANCAttempt;
                    _UnAnsweredSummary.CustomerTodayNANC = (_priorityModel.CustomerCalltime.Date != DateTime.Now.Date) ? 0 : _priorityModel.CustomerTodayNANC;
                }
            }
            catch (Exception ex)
            {
                _UnAnsweredSummary = null;
            }

            return _UnAnsweredSummary;
        }
        public static ResponseAPI IsCallBackAllowed(long LeadId, string CBTime)
        {

            ResponseAPI _Response = new ResponseAPI();
            _Response.status = true;
            bool IsCBTime_diff = false;

            try
            {

                string Key = RedisCollection.IsCallBackAllowed() + ":" + LeadId;
                string obj = RedisHelper.GetRedisData(Key);

                if (obj == null)
                {
                    List<Int16> RestrictCalling = "RestrictCallingProducts".AppSettings().Split(',').Select(Int16.Parse).ToList();
                    List<Int32> HNIGroupIDs = "HNIGroupIDs".AppSettings().Split(',').Select(Int32.Parse).ToList();
                    int CallBackWaitTime = Convert.ToInt32("CallBackWaitTime".AppSettings());

                    MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                    IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                    IMongoFields fields = Fields<PriorityModel>.Include(p => p.LeadID, p => p.ProductID,
                        p => p.Call, p => p.CustomerCalltime, p => p.User, p => p.CallBack);
                    var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);
                    //bool IsOnCall = IsCustomerOnCall(LeadId);

                    if (_priorityModel == null)
                        return _Response;

                    var _LeadPriorityConstants = getPriorityConfigByProduct(_priorityModel.ProductID);

                    if (_LeadPriorityConstants.IsCBRestrictionON == false)// this is  made when there is delay in dailar disposition data .
                        return _Response;

                    //chk Cb diff from current DateTime                     

                    if ((_priorityModel.Call == null || _priorityModel.Call.TotalTT == 0) && _priorityModel.ProductID != 131)
                    {
                        if (_priorityModel.EmailOnly)
                        {
                            return _Response;
                        }
                        _Response.status = false;
                        _Response.message = "Total Talktime = 0. Call back can be marked only if atleast one call has been answered!";
                        return _Response;
                    }
                    else if (_LeadPriorityConstants.TodayExpCall.Contains(DateTime.Now.Date) && _priorityModel.Call != null && _priorityModel.Call.calltime.Date != DateTime.Now.Date)
                    {
                        _Response.status = false;
                        _Response.message = "You are not allowed to set CB on this lead today.";
                    }
                    else if (_priorityModel.Call != null && _priorityModel.Call.calltime.Date == DateTime.Now.Date && GetRestrictLeadAttempts(_priorityModel.LeadID) == 1 && !GetCBTimeDiff(LeadId, CBTime, 120, _priorityModel.Call.calltime))
                    {
                        _Response.status = false;
                        // _Response.message = "Sorry you can add callback on this lead after " + Convert.ToInt16(120 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes) + " mins";
                        _Response.message = "Callback on this lead can be scheduled only for slots available after " + Convert.ToInt16(120 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes) + " mins from now.";

                    }

                    /*
                    else if (RestrictCalling.Contains(_priorityModel.ProductID) && _priorityModel.Call.TodayTalktime < 300 && _priorityModel.Call.calltime.Date == DateTime.Now.Date && _priorityModel.Call.LastNminuteNANCeAttempts == 2 && !GetCBTimeDiff(LeadId, CBTime, 120, _priorityModel.Call.calltime))
                    {
                        _Response.status = false;
                        _Response.message = "Callback on this lead can be scheduled only for slots available after " + Convert.ToInt16(120 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes) + " mins from now.";
                        //_Response.message = "Sorry you can add callback on this lead after " + Convert.ToInt16(120 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes) + " mins";

                    }
                    else if (RestrictCalling.Contains(_priorityModel.ProductID) && _priorityModel.Call.TodayTalktime < 300 && _priorityModel.Call.calltime.Date == DateTime.Now.Date && _priorityModel.Call.LastNminuteNANCeAttempts > 2 && !GetCBTimeDiff(LeadId, CBTime, 180, _priorityModel.Call.calltime))
                    {
                        _Response.status = false;
                        _Response.message = "Callback on this lead can be scheduled only for slots available after " + Convert.ToInt16(180 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes) + " mins from now";
                    } */
                    else if (_priorityModel.CallBack != null && _priorityModel.CallBack.CallBackType == CallBackTypeEnum.CustRequested
                    && _priorityModel.CallBack.CBtime > DateTime.Now
                    && _priorityModel.Call != null
                    && !(_priorityModel.Call.calltime >= _priorityModel.CallBack.CBtime.AddMinutes(-30) && _priorityModel.Call.TalkTime >= 10))
                    {
                        _Response.status = false;
                        _Response.message = "Customer Scheduled call back - Cannot be rescheduled";
                    }
                    else if (_priorityModel.Call != null && _priorityModel.Call.TodayTalktime > 300)
                    {
                        RedisHelper.SetRedisData(Key, Convert.ToString(_Response.status), new TimeSpan(7, 0, 0, 0));
                    }
                }
                else
                {
                    _Response.status = Convert.ToBoolean(obj);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "IsCallBackAllowed", "LeadPrioritizationDLL", "Communication", string.Empty, string.Empty, DateTime.Now, DateTime.Now);

            }

            return _Response;

        }
        public static bool GetCBTimeDiff(long LeadId, string CBTime, Int16 Minutes, DateTime LastCalltime)
        {
            bool result = false;
            try
            {
                if (!string.IsNullOrEmpty(CBTime) && ((Convert.ToDateTime(CBTime)) > DateTime.Now))
                {
                    if (Minutes == 1440 && Convert.ToDateTime(CBTime).Date > DateTime.Now.Date)
                        result = true;
                    else
                        result = (Convert.ToDateTime(CBTime) - LastCalltime).TotalMinutes >= Minutes;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.ToString(), "GetCBTimeDiff", "LeadPrioritizationDLL", "", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }
        public static UserDetails getAgentData(Int64 UserID)
        {
            UserDetails _UserDetails = new UserDetails();
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@userId", UserID);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentDetailsById]", sqlParam);

            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                _UserDetails.EmployeeId = Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]);
                _UserDetails.UserName = Convert.ToString(ds.Tables[0].Rows[0]["UserName"]);
            }
            return _UserDetails;
        }
        public static DataSet GetNotContactedLeads(Int64 UserId, Int16 LastDays)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@UserId", UserId);
            SqlParam[1] = new SqlParameter("@LastDays", LastDays);
            return SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[GetNotContactedLeads]", SqlParam);

        }
        public static bool SkipCustAllLead(Int64 CustomerId, long excludeLeadId, short SkipDuration)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

            // fetch active leads for customer
            var query = Query<PriorityModel>.Where((lead) =>
                lead.CustID == CustomerId
                && lead.IsActive
                && lead.LeadID != excludeLeadId
            );
            IMongoFields fields = Fields<PriorityModel>.Include(p => p.LeadID, p => p.SkipDurationHrs, p => p.SkippingTime);
            var leads = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection());


            // filter leads with skip lesser than new skiptime 
            var filteredLeads = leads
                .Where(lead => lead.SkipDurationHrs == 0 || (lead.SkippingTime.AddMinutes(lead.SkipDurationHrs) < DateTime.Now.AddMinutes(SkipDuration)))
                .ToList();


            foreach (var lead in filteredLeads)
            {
                var updatedSkippingTime = DateTime.Now;
                var updatedSkipDurationHrs = SkipDuration;

                var filter = Query<PriorityModel>.EQ(x => x.LeadID, lead.LeadID);
                var update = Update<PriorityModel>
                    .Set(x => x.SkippingTime, updatedSkippingTime)
                    .Set(x => x.SkipDurationHrs, updatedSkipDurationHrs);
                _CommDB.UpdateDocument(filter, update, DataAccessLibrary.MongoCollection.LPDataCollection());
            }


            return true;
        }

        public static PriorityModel GetLeadDataFromMongo(long leadId)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

            var query = Query<PriorityModel>.Where((lead) => lead.LeadID == leadId);
            return _CommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection());

        }
        public static bool RejectLeadOnDemand(long leadId, string RejectionReason, short rejectionSubStatus)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(leadId));
            sqlParam[1] = new SqlParameter("@Rejectionreason", RejectionReason);
            sqlParam[2] = new SqlParameter("@SubstatusId", rejectionSubStatus);
            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[Reject_Lead_ondemand]", sqlParam);
            return true;
        }
        public static Int16 CanReleaseLead(Int64 LeadID, Int64 UserID)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserID", UserID);
            sqlParam[1] = new SqlParameter("@LeadID", LeadID);
            return Convert.ToInt16(SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CanReleaseLeadV2]", sqlParam));
        }
        public static List<PriorityModel> GetWeeklyExpiredLeads(Int64 UserId, Int16 ProductId)
        {
            List<PriorityModel> leads = null;

            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

            string CurrentCallShift = CalculateCallShift(DateTime.Now);
            DateTime CurrentTime = DateTime.Now;
            DateTime DayAfterTomorrowTime = DateTime.Now.AddDays(2);
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var _ProductLeadPriorityConstants = getPriorityConfigByProduct(ProductId);
            Int16 ActiveRevisitShowTime = _ProductLeadPriorityConstants.ActiveRevisitShowTime;
            short Week1MaxAttempts = _ProductLeadPriorityConstants.Week1MaxAttempts;
            short WeekMaxAttempts = _ProductLeadPriorityConstants.WeekMaxAttempts;

            var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId),
                                Query<PriorityModel>.EQ(p => p.IsActive, true)
                                );
            leads = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection())
                        .Where(x => (x.ActiveLeadSet.Count > 0
                                    && (x.Call != null)
                                    && (x.User.FirstAssignedOn != DateTime.MinValue)
                                    && (Convert.ToInt16(CurrentTime.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays / 7 + 1) == x.Call.Current_Week)
                                    && (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerTodayNANC <= 10) // valid customer unanswered attempts not to picked (max 10)   
                                    && ((x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysNANCAttempt >= 4) // valid unanswered attempts not to picked (max 4) 
                                        || (x.Call.Current_Week == 1 ? x.Call.Week_Attempt >= Week1MaxAttempts : (x.Call.Week_Attempt >= WeekMaxAttempts))
                                        || (x.CustomerCalltime.Date == DateTime.Now.Date && x.CustomerNANCAttempt >= 4) // valid customer unanswered attempts not to picked (max 4) 
                                        )
                                )
                                )
                        .Select(x => new PriorityModel { LeadID = x.LeadID, ReleaseStatus = x.ReleaseStatus, CustName = x.CustName, LeadStatus = GetLeadStatus(x), PrevPolicyExpDate = x.PrevPolicyExpDate }).OrderByDescending(x => x.LeadCreatedOn).ToList();

            return leads;
        }

        public static DataSet GetUserIdByEmployeeId(string empId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@EmployeeId", empId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetUserIdByEmployeeId]", sqlParam);

        }
        public static DataSet GetUserAssignedLeads(Int64 UserID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserID);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUserAssignedLeads]", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public static List<PriorityModel> GetStarLeads(Int64 UserId)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            DateTime ct = DateTime.Now;

            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId), Query<PriorityModel>.GT(p => p.star, 0), Query<PriorityModel>.EQ(p => p.IsActive, true));
            lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection())
                                .Where(x => (x.ActiveLeadSet.Count > 0))
                                    .ToList();

            return lstPriorityModel;

        }
        public static List<PriorityModel> GetExpiringLeads(Int64 UserId, byte productID)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            byte RecentExpiryDayGap = _LeadPriorityConstants.RecentExpiryGap;
            //(Math.Abs(ct.Subtract(x.PrevPolicyExpDate).TotalDays)  <= RecentExpiryDayGap)
            DateTime ct = DateTime.Now;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query.And(Query<PriorityModel>.EQ(p => p.ProductID, productID),
                                       Query<PriorityModel>.EQ(p => p.User.UserID, UserId),
                                      Query<PriorityModel>.EQ(p => p.IsActive, true));
                lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection())
                                   .Where(x =>
                                       //(Math.Abs(ct.Subtract(x.PrevPolicyExpDate).TotalDays) <= RecentExpiryDayGap) 
                                       (x.PrevPolicyExpDate.Date == ct.Date || x.PrevPolicyExpDate == ct.AddDays(RecentExpiryDayGap).Date || x.PrevPolicyExpDate == ct.AddDays(-RecentExpiryDayGap).Date)
                                       && (x.ActiveLeadSet.Count > 0)
                                       && x.LeadPoints > 0)
                                       .ToList();
            }
            catch (Exception ex)
            {

            }
            return lstPriorityModel;

        }
        public static List<PriorityModel> GetCountryIdOfLeads(List<Int64> LeadList)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

                var query = Query<PriorityModel>.In(p => p.LeadID, LeadList);
                IMongoFields fields = Fields<PriorityModel>.Include(p => p.LeadID, p => p.ProductID, p => p.Country, p => p.LeadPoints, p => p.Call, p => p.CustomerNANCAttempt, p => p.CustomerTodayNANC, p => p.CustomerCalltime);
                oPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, fields, DataAccessLibrary.MongoCollection.LPDataCollection()).ToList();

            }
            catch (Exception ex)
            {

            }

            return oPriorityModel;
        }
        public static long GetTLCallingNo(string UserId)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", Convert.ToInt64(UserId));
                return Convert.ToInt64(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetTLCallingNo", sqlParam));
            }
            catch (Exception e)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(UserId), e.ToString(), "GetTLCallingNo_error", "GetTLCallingNo_error", "LeadPrioritizationDLL", "", "", requestTime, DateTime.Now);
                return 0;

            }
        }
        public static DataSet GetLastCalledLeads(Int64 UserId, Int16 productID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@UserID", UserId);
                sqlParam[1] = new SqlParameter("@ProductId", productID);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentLatestCalledLeads]", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public static ResponseAPI StarLead(Int64 UserId, Int16 productID, Int64 LeadID, byte Star)
        {
            ResponseAPI _Response = new ResponseAPI();
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            _Response.status = true;
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            bool IsUpdate = true;
            try
            {
                byte starLimit = _LeadPriorityConstants.StarLeadsLimit;
                IMongoQuery query;
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                if (Star > 0)
                {
                    query = Query.And(Query<PriorityModel>.EQ(p => p.User.UserID, UserId), Query<PriorityModel>.GT(p => p.star, 0), Query<PriorityModel>.EQ(p => p.IsActive, true));
                    lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection())
                                       .Where(x => (x.ActiveLeadSet.Count > 0))
                                           .ToList();
                    if (lstPriorityModel.Count >= starLimit)
                    {
                        IsUpdate = false;
                        _Response.status = false;
                        _Response.message = _LeadPriorityConstants.msg.ImportantLeadLimitMsg;
                    }
                }
                if (IsUpdate)
                {
                    query = Query<PriorityModel>.EQ(x => x.LeadID, LeadID);
                    UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.star, Star);
                    _CommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }
            }
            catch (Exception ex)
            {

            }
            return _Response;
        }
        public static void LogSkipHistory(long leadid, Int16 skipDuration, Int64 UserId)
        {

            string Connectionstring = ConnectionClass.LivesqlConnection();
            //    string strQuery = "SELECT COUNT(1) FROM CTC.DailerLeadMapping (NOLOCK) WHERE LeadID=@LeadID AND LeadType=1";
            string strQuery = "INSERT INTO [PBCroma].[MTX].[SkipLeadHistory](LEADID,SKIPDURATION ,SKIPTIME,UserId) VALUES (@LeadID,@SkipDuration,@SkipTime,@UserId)";
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", leadid);
            SqlParam[1] = new SqlParameter("@SkipDuration", skipDuration);
            SqlParam[2] = new SqlParameter("@SkipTime", DateTime.Now);
            SqlParam[3] = new SqlParameter("@UserId", UserId);
            SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
        }
        public static DataSet GetActiveSetLeads(long ParentId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", ParentId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetActiveSetLeadInfo]", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public static string GetCustConnectTime(long LeadId)
        {
            string result = string.Empty;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);
                var ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustConnectTime]", sqlParam);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    result = Convert.ToString(ds.Tables[0].Rows[0]["CustConnectTime"]);
            }
            catch (Exception ex)
            {
                return result;
            }
            return result;
        }

        public static bool CheckRenwalAgent(long Userid)
        {
            bool isRenewalAgent = false;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@Userid", Userid);
                var ds = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckRenwalAgent]", sqlParam);
                if (ds != null)
                    isRenewalAgent = Convert.ToBoolean(ds.ToString());
            }
            catch (Exception ex)
            {
                return isRenewalAgent;
            }
            return isRenewalAgent;
        }

        public static DataSet GetUserIdFromLeadId(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadAssignedAgent]", sqlParam);

        }

        public static bool ChkSystemCrossSell(long leadID)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", leadID);
            var ds = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[ChkSystemCrossSell]", sqlParam);
            return Convert.ToBoolean(ds);
        }

        public static List<SentimentCoolingLogic> GetSentimentCoolingPeriodMaster()
        {
            List<SentimentCoolingLogic> sentimentCoolingLogics = new();

            string collectionName = RedisCollection.SentimentCoolingPeriod();
            if (MemoryCache.Default[collectionName] != null)
            {
                sentimentCoolingLogics = (List<SentimentCoolingLogic>)MemoryCache.Default.Get(collectionName);
            }
            else
            {
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSentimentCoolingPeriodMaster]");

                if (ds != null && ds.Tables.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        SentimentCoolingLogic sentimentCoolingLogic = new()
                        {
                            MinExpDays = Convert.ToInt16(row["MinExpDays"]),
                            MaxExpDays = Convert.ToInt16(row["MaxExpDays"]),
                            CoolingPeriod = Convert.ToInt16(row["CoolingPeriod"]),
                            DNCCount = Convert.ToInt16(row["DNCCount"])
                        };
                        sentimentCoolingLogics.Add(sentimentCoolingLogic);
                    }
                }

                if (sentimentCoolingLogics.Count > 0)
                {
                    CommonCache.GetOrInsertIntoCache(sentimentCoolingLogics, collectionName, 8 * 60);
                }
            }

            return sentimentCoolingLogics;
        }

        public static DataSet GetCustInterestedProducts(long custID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@CustomerId", custID);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustInterestedProducts]", sqlParam);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return null;
            }
        }

        public static Int16 GetNANCAttempts(long leadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadID", leadId);

                var data = Convert.ToInt16(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetNANCAttempts]", sqlParam));
                return data;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return 10;
            }
        }
        public static void InsertUpdateCustNANCInMongo(CustomerNANC oCustomerNANC)
        {
            MongoHelper objCommDB = new(SingletonClass.OneLeadDB());
            var query = Query<CustomerNANC>.Where((CustomerData) => CustomerData.CustID == oCustomerNANC.CustID);
            var customerNANCData = objCommDB.FindOneDocument<CustomerNANC>(query, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());



            if (customerNANCData != null)
            {
                short TodayNANCCount = (short)(customerNANCData.TodayNANCCount + 1);

                if (customerNANCData.CallDate == oCustomerNANC.CallDate) return;  // data already updated

                if (customerNANCData.CallDate.Date != DateTime.Now.Date) { // Today's first NANC
                    TodayNANCCount = 1;
                }
                UpdateBuilder<CustomerNANC> update = Update<CustomerNANC>.Set(p => p.CallDate, oCustomerNANC.CallDate)
                                                                         .Set(p => p.TodayNANCCount, TodayNANCCount)
                                                                         .Set(p => p.UpdatedOn, DateTime.Now);

                objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());
            }
            else
            {
                oCustomerNANC.TodayNANCCount = 1;
                objCommDB.InsertData(oCustomerNANC, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());
            }
        }

        public static DataSet GetLeadDetails_SQL(long leadId) {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@leadId", leadId);
            string strQuery = "SELECT CustomerId FROM Matrix.CRM.LeadDetails(NOLOCK) WHERE LeadID=@LeadId";
            DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            return ds;
        }

        public static short GetCustNANCAttemptsMongo(long custID)
        {
            short TodayNANCCount = 0;

            try
            {
                MongoHelper objCommDB = new(SingletonClass.OneLeadDB());

                var query = Query<CustomerNANC>.Where((CustomerData) => CustomerData.CustID == custID);
                var customerNANCData = objCommDB.FindOneDocument<CustomerNANC>(query, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());

                if (customerNANCData != null && customerNANCData.CallDate != null && customerNANCData.CallDate.Date == DateTime.Now.Date)
                {
                    TodayNANCCount = customerNANCData.TodayNANCCount;
                }
            }
            catch
            {
                TodayNANCCount = 10;
            }
            return TodayNANCCount;
        }

        public static void TrackAssignAndStatusIDs(long ID, string EventName)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@ID", ID);
            SqlParam[1] = new SqlParameter("@EventName", EventName);
            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[Track_Mongo_AssignAndStatusIDs]", SqlParam);
        }

        public static void InsertLeadLog(PriorityModel lead)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadID", lead.LeadID);
            SqlParam[1] = new SqlParameter("@Comments", "DNC Cooling Period of " + lead.DNC.CoolingPeriod + " days");
            SqlParam[2] = new SqlParameter("@EventType", 7);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[MTX].[SaveLeadHistory]",
                                      SqlParam);
        }

        public static string GetVNCallingCompany(string virtualNo, out string serverIp)
        {
            string callingCompany = string.Empty;
            serverIp = string.Empty;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@VirtualNo", virtualNo);

                var data = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[GetVirtualNumberByLeadId]",
                                                    SqlParam);
                if (data != null && data.Tables.Count > 0)
                {
                    callingCompany = data.Tables[0].Rows[0]["CallingCompany"].ToString();
                    serverIp = data.Tables[0].Rows[0]["ServerIP"].ToString();
                }
            }
            catch { }
            return callingCompany;
        }
        public static PriorityModel GetLeadCallFreqDetails(long leadId)
        {
            string frequencyProcess = string.Empty;
            PriorityModel priorityModel = null;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

                var query = Query<PriorityModel>.EQ(x => x.LeadID, leadId);
                var fields = Fields<PriorityModel>.Include(x => x.CallFreqProcess, x => x.User, x => x.CallBack);
                priorityModel = _CommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);

                // if (priorityModel != null && !string.IsNullOrEmpty(priorityModel.CallFreqProcess))
                // {
                //     frequencyProcess = priorityModel.CallFreqProcess;
                // }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    "",
                    leadId,
                    ex.ToString(),
                    "GetCallFreqProcess",
                    "LeadPrioritizationDLL",
                    "OneLeadPriority",
                    "",
                    string.Empty,
                    DateTime.Now,
                    DateTime.Now
                );
            }
            return priorityModel;
        }
        public static void UpdateCallFreqProcess(long leadId, string frequencyProcess)
        {
            IMongoQuery query;
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());

            query = Query<PriorityModel>.EQ(x => x.LeadID, leadId);
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.CallFreqProcess, frequencyProcess);
            _CommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
        }

        public static void InsertRenewalCallAnalysis(RenewalIntentRequest request)
        {
            try
            {
                DateTime now = DateTime.Now;

                string requestJson = JsonConvert.SerializeObject(request);
                var renewalAnalysis = BsonDocument.Parse(requestJson);

                renewalAnalysis["LeadId"] = new BsonInt64(request.LeadId); // int64
                renewalAnalysis["CallDataID"] = new BsonInt64(request.CallDataID);
                renewalAnalysis.Add("ts", now);

                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                objCommDB.InsertData(renewalAnalysis, DataAccessLibrary.MongoCollection.RenewalCallAnalysis());
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    "",
                    request.LeadId,
                    ex.ToString(),
                    "InsertRenewalAnalysisToMongo",
                    "LeadPrioritizationDLL",
                    "OneLeadPriority",
                    JsonConvert.SerializeObject(request),
                    string.Empty,
                    DateTime.Now,
                    DateTime.Now
                );
            }
        }
        public static int getDNCCount(Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            var obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[MTX].[getDNCCount]",
                                      SqlParam);

            return Convert.ToInt32(obj);

        }

        public static void LogLeadCallFreqProcess(long leadId, string frequencyProcess, long CallDataID)
        {
            try
            {
                SqlParameter[] sqlParams = new SqlParameter[3];
                sqlParams[0] = new SqlParameter("@LeadID", leadId);
                sqlParams[1] = new SqlParameter("@FrequencyProcess", frequencyProcess);
                sqlParams[2] = new SqlParameter("@CallDataID", CallDataID);

                string query = "INSERT INTO [MTX].[LeadCallFrequencyProcess] ([LeadID], [FrequencyProcess],[CallDataID]) VALUES (@LeadID, @FrequencyProcess, @CallDataID)";
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParams);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    "",
                    leadId,
                    ex.ToString(),
                    "LogLeadCallFreqProcess",
                    "LeadPrioritizationDLL",
                    "OneLeadPriority",
                    frequencyProcess,
                    string.Empty,
                    DateTime.Now,
                    DateTime.Now
                );
            }
        }
        
        public static bool IsTermHNIExp(PriorityModel lead, short _GroupID)
        {

            if (lead.Income!=null && lead.Income >= 1500000 && lead.LeadID % 20 == 0 && lead.ProductID == 7)
            {
                return true;
            }
            return false;
        }
    }
}

