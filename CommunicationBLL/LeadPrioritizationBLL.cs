﻿using System.Text;
using DataAccessLayer;
using DataAccessLibrary;
using DataHelper;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using Newtonsoft.Json;
using OneLeadPriorityData;
using PropertyLayers;
using ReadXmlProject;
using DialerDataUpdate;
using System.Data;
using System.Dynamic;
using System.Globalization;
using Helper;
using System.Reflection.Emit;
using Google.Apis.Sheets.v4.Data;

namespace CommunicationBLL;

public class LeadPrioritizationBLL : ILeadPrioritizationBLL
{
    // Global object
    public LeadPriorityConstants oLeadPriorityConstants;

    public LeadPrioritizationBLL()
    {
        oLeadPriorityConstants = DataAccessLibrary.PriorityConfig.getConfig();
    }

    public PriorityModel? GetOneLead(long UserId, short ProductId, string Source)
    {
        bool isRenewalGroup = false;
        short GroupID = 0;
        short processId = 0;
        DateTime requestTime = DateTime.Now;
        List<Next5WidgetLead>? BookedLeads = null;
        List<short> healthProducts = new() { 106, 118, 130 };
        List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
        try
        {
            List<short> LeadPriorityProduct = "LeadPriorityProduct".AppSettings().Split(',').Select(short.Parse).ToList();

            OneLeadObj oneLeadObj = LeadPrioritizationDLL.GetOneLeadObj(UserId);
            if (oneLeadObj != null)
            {
                processId = oneLeadObj.ProcessId;
                GroupID = oneLeadObj.GroupId;
            }
            else
            {
                var ds = LeadPrioritizationDLL.GetGroupNameandID(UserId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    processId = (ds.Tables[0].Rows[0]["ProcessID"] == null || ds.Tables[0].Rows[0]["ProcessID"] == DBNull.Value) ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["ProcessID"]);
                    GroupID = Convert.ToInt16(ds.Tables[0].Rows[0]["UserGroupID"]);
                }
            }

            if (processId == 4)
                isRenewalGroup = true;

            if (healthProducts.Contains(ProductId))
                ProductId = 2;
            else if (!LeadPriorityProduct.Contains(ProductId))
                ProductId = 117;

            OneLeadParams oneLeadParams = new()
            {
                Source = Source
            };

            var IOneleadprovider = OneLeadProvider.getoneLeadProvide(ProductId, oneLeadParams, isRenewalGroup, GroupID, processId, null);
            var _LeadPriorityConstants = IOneleadprovider.getPriorityConfigByProduct(ProductId);
            lstPriorityModel = IOneleadprovider.getOneLead(ProductId, UserId, _LeadPriorityConstants.PriorityQueueSize);

            PrepNDumpLeadForNext5LdWidget(UserId, lstPriorityModel, BookedLeads, string.Empty);// dumping next 5 leads for meteor Widget

            if (lstPriorityModel != null && lstPriorityModel.Count > 0)
            {
                var priorityObj = lstPriorityModel[0];
                PriorityModel priorityModel = new()
                {
                    LeadID = priorityObj.LeadID,
                    CustID = priorityObj.CustID,
                    ProductID = priorityObj.ProductID,
                    LeadCategory = priorityObj.LeadCategory,
                    Reason = GetLeadPriorityReason(priorityObj)
                };

                return priorityModel;
            }
            else if (oneLeadObj == null || !oneLeadObj.NoLeadPopUp)
            {
                LeadPrioritizationDLL.UpDateNoLeadPopUp(UserId);
                if (oneLeadObj == null)
                    oneLeadObj = new() { GroupId = GroupID, ProcessId = processId, NoLeadPopUp = true };
                else
                    oneLeadObj.NoLeadPopUp = true;

                LeadPrioritizationDLL.MarkNoLeadObjInRedis(UserId, oneLeadObj);
            }

        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("0", UserId, ex.ToString(), "GetOneLead_error", "GetOneLead", "Automation", "", string.Empty, requestTime, DateTime.Now);
        }
        finally
        {
            Int64[] UserIDsArr = { 53071, 53591, 53790, 55430, 15521 };
            if (UserIDsArr.Contains(UserId))
            {
                List<dynamic> _objlst = new List<dynamic>();
                foreach (var _priorityModel in lstPriorityModel)
                {
                    dynamic _obj = new ExpandoObject();
                    _obj.LeadID = _priorityModel.LeadID;
                    _obj.Leadcategory = _priorityModel.LeadCategory;
                    _obj.Reason = GetLeadPriorityReason(_priorityModel);
                    _objlst.Add(_obj);
                    if (_objlst.Count > 4)
                    {
                        break;
                    }
                }

                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, UserId, string.Empty, "GetOneLead", "GetOneLead", "Automation", "", JsonConvert.SerializeObject(_objlst), requestTime, DateTime.Now);
            }
        }
        return null;
    }

    public void PrepNDumpLeadForNext5LdWidget(long UserId, List<PriorityModel> lstPriorityModel, List<Next5WidgetLead>? BookedLeads, string BMSUserToken)
    {
        DateTime requestTime = DateTime.Now;
        try
        {
            List<Next5WidgetLead> lstNext5WidgetLead = new();
            List<Next5WidgetLead> data = new();
            const int secdiff = 180;
            bool insertData = false;
            UserNext5Leads oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(UserId);

            CheckManualAddedLeads(oUserNext5Leads, data, secdiff);

            if (lstPriorityModel != null && lstPriorityModel.Count > 0)
            {
                foreach (PriorityModel lead in lstPriorityModel.Where(x => x.LeadID > 0).Take(4))
                {
                    Next5WidgetLead oNext5WidgetLead;
                    if (data.Count > 0 && data.Find(x => x.LeadId == lead.LeadID) is var leadObj && leadObj != null)
                    {
                        leadObj.Priority = 0;
                        leadObj.Reason = GetLeadPriorityReason(lead);
                        var leadCatName = Enum.GetName(typeof(LeadCategoryEnum), lead.LeadCategory);
                        if (!string.IsNullOrEmpty(leadCatName))
                            leadObj.ReasonId = Convert.ToInt16(Enum.Parse(typeof(LeadCategoryEnum), leadCatName));

                        continue;
                    }

                    else if (BookedLeads != null && BookedLeads.Count > 0 && BookedLeads.Find(x => x.LeadId == lead.LeadID) is var bookedObj && bookedObj != null)
                    {
                        oNext5WidgetLead = bookedObj;
                        oNext5WidgetLead.Counter = lead.Counter;
                    }

                    else
                    {
                        oNext5WidgetLead = new Next5WidgetLead
                        {
                            LeadId = lead.LeadID,
                            Name = lead.CustName,
                            Reason = GetLeadPriorityReason(lead),
                            ts = DateTime.Now,
                            CustomerId = lead.CustID,
                            ProductId = lead.ProductID,
                            EmailOnly = lead.EmailOnly,
                            Counter = lead.Counter
                        };

                        string? reason = Enum.GetName(typeof(LeadCategoryEnum), lead.LeadCategory);
                        if (!string.IsNullOrEmpty(reason))
                            oNext5WidgetLead.ReasonId = Convert.ToInt16(Enum.Parse(typeof(LeadCategoryEnum), reason));
                    }

                    lstNext5WidgetLead.Add(oNext5WidgetLead);
                }
            }

            if (data.Count > 0)
                lstNext5WidgetLead.AddRange(data);

            if (oUserNext5Leads != null)
            {
                oUserNext5Leads.Leads = lstNext5WidgetLead.Count > 0 ? lstNext5WidgetLead : null;
                oUserNext5Leads.BookedLeads = (BookedLeads != null && BookedLeads.Count > 0) ? BookedLeads : null;
                oUserNext5Leads.BMSUserToken = BMSUserToken;
            }

            LeadPrioritizationDLL.InsertUpdateNext5Leads(UserId, oUserNext5Leads, insertData);

        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("0", UserId, ex.ToString(), "PrepNDumpLeadForNext5LdWidget_error", "PrepNDumpLeadForNext5LdWidget_error", "Automation", "", string.Empty, requestTime, DateTime.Now);
        }
    }

    //Reason of Lead coming in priority
    private string GetLeadPriorityReason(PriorityModel oPriorityModel)
    {
        string reason = string.Empty;
        try
        {

            switch (oPriorityModel.LeadCategory)
            {
                case LeadCategoryEnum.PaymentCB:
                    reason = oLeadPriorityConstants.msg.PaymentCB;
                    break;
                case LeadCategoryEnum.ActiveRevisit:
                    reason = oLeadPriorityConstants.msg.ActiveRevisit + GetRevisitType(oPriorityModel.Revisit.RevisitType);
                    break;
                case LeadCategoryEnum.ActiveNew:
                    reason = oLeadPriorityConstants.msg.ActiveNew;
                    break;
                case LeadCategoryEnum.ActiveCB:
                    reason = oLeadPriorityConstants.msg.ActiveCB;
                    break;
                case LeadCategoryEnum.PassiveCB:
                    reason = oLeadPriorityConstants.msg.PassiveCB;
                    break;
                case LeadCategoryEnum.PassiveRevisit:
                    reason = oLeadPriorityConstants.msg.PassiveRevisit + GetRevisitType(oPriorityModel.Revisit.RevisitType);
                    break;
                case LeadCategoryEnum.PassiveNew:
                    reason = oLeadPriorityConstants.msg.PassiveNew;
                    break;
                case LeadCategoryEnum.SecondAttemptPCB:
                    reason = oLeadPriorityConstants.msg.SecondAttemptPCB;
                    break;
                case LeadCategoryEnum.SecondAttemptActvCB:
                    reason = oLeadPriorityConstants.msg.SecondAttemptActvCB;
                    break;
                case LeadCategoryEnum.SecondAttemptActvNew:
                    reason = oLeadPriorityConstants.msg.SecondAttemptActvNew;
                    break;
                case LeadCategoryEnum.SecondAttemptActvRevisit:
                    reason = oLeadPriorityConstants.msg.SecondAttemptActvRevisit + GetRevisitType(oPriorityModel.Revisit.RevisitType);
                    break;
                case LeadCategoryEnum.SecondAttemptPasvCB:
                    reason = oLeadPriorityConstants.msg.SecondAttemptPasvCB;
                    break;
                case LeadCategoryEnum.SecondAttemptPasvNew:
                    reason = oLeadPriorityConstants.msg.SecondAttemptPasvNew;
                    break;
                case LeadCategoryEnum.SecondAttemptPasvRevisit:
                    reason = oLeadPriorityConstants.msg.SecondAttemptPasvRevisit + GetRevisitType(oPriorityModel.Revisit.RevisitType);
                    break;
                case LeadCategoryEnum.UnansweredLeads:
                    reason = oLeadPriorityConstants.msg.UnansweredLeads;
                    break;
                case LeadCategoryEnum.RestLeads_1:
                    reason = oLeadPriorityConstants.msg.RestLeads_1;
                    break;
                case LeadCategoryEnum.RestLeads_2:
                    reason = oLeadPriorityConstants.msg.RestLeads_2;
                    break;
                case LeadCategoryEnum.SecondAttemptRestLeads:
                    reason = oLeadPriorityConstants.msg.SecondAttemptRestLeads;
                    break;
                case LeadCategoryEnum.CallReleasedLeads:
                    reason = oLeadPriorityConstants.msg.CallReleasedLeads;
                    break;
                case LeadCategoryEnum.RecentExpiry:
                    reason = oLeadPriorityConstants.msg.RecentExpriyLeads;
                    break;
                case LeadCategoryEnum.SkippedLeads:
                    reason = oLeadPriorityConstants.msg.SkippedLeads;
                    break;
                case LeadCategoryEnum.FutureCallBackLeads:
                    reason = oLeadPriorityConstants.msg.FutureCallBackLeads;
                    break;
                case LeadCategoryEnum.BdayLeads:
                    reason = oLeadPriorityConstants.msg.BdayLeads;
                    break;
                case LeadCategoryEnum.CTCRevisit:
                    reason = oLeadPriorityConstants.msg.CTCRevisit;
                    break;
                case LeadCategoryEnum.SecondAttemptRevisitCTC:
                    reason = oLeadPriorityConstants.msg.SecondAttemptRevisitCTC;
                    break;
                case LeadCategoryEnum.EmailRevisit:
                    reason = oLeadPriorityConstants.msg.EmailRevisit;
                    break;
                case LeadCategoryEnum.SecondAttemptEmailRevisit:
                    reason = oLeadPriorityConstants.msg.SecondAttemptEmailRevisit;
                    break;
                case LeadCategoryEnum.UnansweredRecentLeads:
                    reason = oLeadPriorityConstants.msg.UnansweredLeads;
                    break;
                case LeadCategoryEnum.PaymentFailure:
                    reason = oLeadPriorityConstants.msg.PaymentFailure;
                    break;
                case LeadCategoryEnum.BajajCustomerRevisit:
                    reason = oLeadPriorityConstants.msg.BajajCustomerRevisit;
                    break;
                case LeadCategoryEnum.MissedCB:
                    reason = oLeadPriorityConstants.msg.MissedCB;
                    break;
                case LeadCategoryEnum.BookedCB:
                    reason = oLeadPriorityConstants.msg.BookedCB;
                    break;
                case LeadCategoryEnum.TicketUpdate:
                    reason = oLeadPriorityConstants.msg.TicketUpdate;
                    break;
                case LeadCategoryEnum.StatusChange:
                    reason = oLeadPriorityConstants.msg.StatusChange;
                    break;
                case LeadCategoryEnum.NoLeadPopup:
                    reason = oLeadPriorityConstants.msg.NoLeadPopup;
                    break;
                case LeadCategoryEnum.ProposalError:
                    reason = oLeadPriorityConstants.msg.ProposalError;
                    break;
                case LeadCategoryEnum.QuoteShared:
                    reason = oLeadPriorityConstants.msg.QuoteShared;
                    break;
                case LeadCategoryEnum.RestPriorityLeads:
                    reason = oLeadPriorityConstants.msg.RestPriorityLeads;
                    break;
                case LeadCategoryEnum.RevisionShared:
                    reason = oLeadPriorityConstants.msg.RevisionShared;
                    break;
                case LeadCategoryEnum.TodayExpiry:
                    reason = oLeadPriorityConstants.msg.TodayExpiry;
                    break;
                case LeadCategoryEnum.SecondAttemptTodayExpiry:
                    reason = oLeadPriorityConstants.msg.SecondAttemptTodayExpiry;
                    break;
                case LeadCategoryEnum.PFFilled:
                    reason = oLeadPriorityConstants.msg.PFFilled;
                    break;
                case LeadCategoryEnum.SecondAttemptPFFilled:
                    reason = oLeadPriorityConstants.msg.SecondAttemptPFFilled;
                    break;
                case LeadCategoryEnum.RMLeads:
                    reason = oLeadPriorityConstants.msg.RMLeads;
                    break;
                case LeadCategoryEnum.CancelBookedLead:
                    reason = oLeadPriorityConstants.msg.CancelBookedLead;
                    break;
                case LeadCategoryEnum.SOSDocTickets:
                    reason = oPriorityModel.Reason;
                    break;
                case LeadCategoryEnum.SOSRest:
                    reason = oPriorityModel.Reason;
                    break;
                case LeadCategoryEnum.ServiceCB:
                    reason = oLeadPriorityConstants.msg.ServiceCB;
                    break;
                case LeadCategoryEnum.VisitToday:
                    reason = oLeadPriorityConstants.msg.VisitToday;
                    break;
                case LeadCategoryEnum.SecondAttemptVisitToday:
                    reason = oLeadPriorityConstants.msg.SecondAttemptVisitToday;
                    break;
                case LeadCategoryEnum.ConfirmVisit:
                    reason = oLeadPriorityConstants.msg.ConfirmVisit;
                    break;
                case LeadCategoryEnum.SecondAttemptConfirmVisit:
                    reason = oLeadPriorityConstants.msg.SecondAttemptConfirmVisit;
                    break;
                case LeadCategoryEnum.MissedVisit:
                    reason = oLeadPriorityConstants.msg.MissedVisit;
                    break;
                case LeadCategoryEnum.SecondAttemptMissedVisit:
                    reason = oLeadPriorityConstants.msg.SecondAttemptMissedVisit;
                    break;
                case LeadCategoryEnum.VisitUpdate:
                    reason = oLeadPriorityConstants.msg.VisitUpdate;
                    break;
                case LeadCategoryEnum.SecondAttemptVisitUpdate:
                    reason = oLeadPriorityConstants.msg.SecondAttemptVisitUpdate;
                    break;
                case LeadCategoryEnum.FOSChurn:
                    reason = oLeadPriorityConstants.msg.FOSChurn;
                    break;
                case LeadCategoryEnum.SecondAttemptFOSChurn:
                    reason = oLeadPriorityConstants.msg.SecondAttemptFOSChurn;
                    break;
                case LeadCategoryEnum.CusatomerCB:
                    reason = oLeadPriorityConstants.msg.CustomerCB;
                    break;
                case LeadCategoryEnum.ExpiryLogic:
                    reason = oLeadPriorityConstants.msg.RestLeads_1;
                    break;
                case LeadCategoryEnum.ExpiryLogicold:
                    reason = oLeadPriorityConstants.msg.RestLeads_1;
                    break;
                case LeadCategoryEnum.RenewalRest:
                    reason = oLeadPriorityConstants.msg.RestLeads_1;
                    break;

            }
        }

        catch (Exception)
        {

        }

        return reason;
    }


    private string GetRevisitType(RevisitType enumRevisitType)
    {
        string Result = string.Empty;
        try
        {
            switch ((byte)enumRevisitType)
            {
                case (byte)RevisitType.WebVisit:
                    Result = " - " + oLeadPriorityConstants.msg.RevisitMsg.Website;
                    break;

                case (byte)RevisitType.Inbound:
                    Result = " - " + oLeadPriorityConstants.msg.RevisitMsg.Inbound;
                    break;

                case (byte)RevisitType.Email:
                    Result = " - " + oLeadPriorityConstants.msg.RevisitMsg.EmailReply;
                    break;
                case (byte)RevisitType.QuoteShared:
                    Result = " - " + oLeadPriorityConstants.msg.RevisitMsg.QuoteShared;
                    break;
                case (byte)RevisitType.RevisionShared:
                    Result = " - " + oLeadPriorityConstants.msg.RevisitMsg.RevisionShared;
                    break;
            }
        }
        catch (Exception)
        {

        }

        return Result;
    }

    public static string GetLeadStatusName(byte StatusID)
    {
        return StatusID switch
        {
            1 => "New",
            2 => "Valid",
            3 => "Contacted",
            4 => "Interested",
            11 => "Prospect",
            _ => "",
        };
    }

    private static void CheckManualAddedLeads(UserNext5Leads oUserNext5Leads, List<Next5WidgetLead> data, int secdiff)
    {
        List<string> BookedReasons = new() { "bookedlead", "booked lead" };
        if (oUserNext5Leads != null && oUserNext5Leads.Leads != null && oUserNext5Leads.Leads.Count > 0)
        {
            data.AddRange(oUserNext5Leads.Leads.FindAll(x => (!string.IsNullOrEmpty(x.Reason)) && BookedReasons.Contains(x.Reason.ToLower()) && (DateTime.Now - x.ts.ToLocalTime()).TotalSeconds < secdiff && string.IsNullOrEmpty(x.CallStatus)).DistinctBy(x => x.LeadId).Take(1));
            data.AddRange(oUserNext5Leads.Leads.FindAll(x => (!string.IsNullOrEmpty(x.Reason)) && x.Reason.ToLower() == "manual added" && string.IsNullOrEmpty(x.CallStatus)).DistinctBy(x => x.LeadId).Take(5));
            data.AddRange(oUserNext5Leads.Leads.FindAll(x => (!string.IsNullOrEmpty(x.Reason)) && x.Reason.ToLower() == "CTC Lead" && string.IsNullOrEmpty(x.CallStatus)).DistinctBy(x => x.LeadId));
        }

    }


    public List<PriorityModel>? GetAgentAllLeads(long UserId, short ProductId, bool showCallableLeads, string source)
    {
        bool isRenewalAgent = false;
        if (UserId == 0)
            return null;

        else if (showCallableLeads == true)
        {
            LeadPrioritizationDLL.UpDateNoLeadPopUp(Convert.ToInt64(UserId));
        }

        if (ProductId == 2 && (source == "pbchat" || source == "whatsapp"))
        {
            isRenewalAgent = LeadPrioritizationDLL.CheckRenwalAgent(Convert.ToInt64(UserId));
        }

        return LeadPrioritizationDLL.GetAgentAllLeads(Convert.ToInt64(UserId), Convert.ToInt16(ProductId), showCallableLeads, isRenewalAgent);
    }

    public bool MarkRetentionLeads(List<PriorityModel> leads)
    {
        bool status = true;
        DateTime requestTime = DateTime.Now;
        MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
        UpdateBuilder<PriorityModel>? update = null;
        foreach (PriorityModel leadData in leads)
        {
            if (leadData != null && leadData.LeadID > 0)
            {
                try
                {
                    var query = Query<PriorityModel>.EQ(x => x.LeadID, leadData.LeadID);
                    update = Update<PriorityModel>.Set(x => x.RetentionOn, DateTime.Now);

                    _CommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }
                catch (Exception ex)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("0", leadData.LeadID, ex.ToString(), "MarkRetentionLeads", "MarkRetentionLeads", "Automation", "", string.Empty, requestTime, DateTime.Now);
                }
            }
        }

        return status;
    }

    //This Method update the mongo Priority Model
    public void UpdatePriorityModelNLeadPoint(PriorityModel reqPriorityModel)
    {
        StringBuilder sb = new StringBuilder();
        string strexception = string.Empty;
        DateTime RequestDatetime = DateTime.Now;
        Int16 WeekAttempts = 0;
        StringBuilder str = new StringBuilder();
        try
        {
            sb.Append("LeadId: " + reqPriorityModel.LeadID + ", Event= " + reqPriorityModel.EventType + "\r\n");
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());

            //return if leadid is null or 0
            if (reqPriorityModel == null || reqPriorityModel.LeadID == 0) return;

            PriorityModel respPriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(reqPriorityModel.LeadID);// Mongo StoredObject
            IMongoQuery query;
            query = Query<PriorityModel>.EQ(p => p.LeadID, reqPriorityModel.LeadID);
            UpdateBuilder<PriorityModel> update = null;
            if (respPriorityModel == null)
            {
                if (reqPriorityModel.EventType == EventTypeEnum.Assignment)//first time assignment
                {
                    Int16 CustomerNANCAttempt = 0, CustomerTodayNANC = 0, CustrespCount = 0;
                    DateTime CustomerCallTime = DateTime.MinValue;
                    getCustNANCAttempts(reqPriorityModel.CustID, reqPriorityModel.LeadID, ref CustomerNANCAttempt, ref CustomerTodayNANC, ref CustrespCount, ref CustomerCallTime);
                    if (CustomerCallTime.Date == DateTime.Now.Date)
                    {
                        reqPriorityModel.CustomerNANCAttempt = CustomerNANCAttempt;
                        reqPriorityModel.CustomerTodayNANC = CustomerTodayNANC;
                        reqPriorityModel.CustomerCalltime = CustomerCallTime;
                    }

                    LeadPrioritizationDLL.InsertPriorityModelInMongo(reqPriorityModel);
                    UpdateLeadPoint(reqPriorityModel, respPriorityModel, 0);
                    LeadPrioritizationDLL.InsertUpdateRevisitPageInfo(string.Empty, reqPriorityModel.LeadID, reqPriorityModel.User.UserID, false, "", reqPriorityModel.LeadID, 0, DateTime.Now);// when lead is assigned UserId will be updated of already visitedLeads
                    //LeadPrioritizationDLL.TrackAssignAndStatusIDs(reqPriorityModel.User.AssignedID, "ASSIGNMENT");
                }
            }
            else
            {
                reqPriorityModel.ProductID = respPriorityModel.ProductID;
                reqPriorityModel.CustID = respPriorityModel.CustID;
                var _ProductWiseCOnstant = LeadPrioritizationDLL.getPriorityConfigByProduct(respPriorityModel.ProductID);
                str.Append("EventType Check Start " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                switch (reqPriorityModel.EventType)
                {
                    case EventTypeEnum.Revisit:
                        if (respPriorityModel.Revisit == null || (DateTime.Now - respPriorityModel.Revisit.ts).TotalMinutes > oLeadPriorityConstants.RevisitMintsGap)
                        {
                            update = updateRevist(respPriorityModel, reqPriorityModel, ref WeekAttempts);
                        }
                        else
                            update = null;

                        break;

                    case EventTypeEnum.CallBack:
                        update = Update<PriorityModel>
                                .Set(p => p.CallBack.ts, reqPriorityModel.CallBack.ts)
                                .Set(p => p.CallBack.CallBackType, reqPriorityModel.CallBack.CallBackType)
                                .Set(p => p.CallBack.CBtime, reqPriorityModel.CallBack.CBtime)
                                .Set(p => p.CallBack.Duration, reqPriorityModel.CallBack.Duration)
                                .Set(p => p.CallBack.IsPaymentCB, reqPriorityModel.CallBack.IsPaymentCB)
                                .Set(p => p.IsLastBucketRest, false);

                        break;
                    case EventTypeEnum.Call:

                        Int16 ValidUnasnweredAttempt = -1;                                                

                        if (reqPriorityModel.Call != null && reqPriorityModel.Call.Duration == 0 && reqPriorityModel.Call.TalkTime > 0)// for handling dailer bug ..they are sending in some case dur=0 and talktime >0
                            reqPriorityModel.Call.Duration = reqPriorityModel.Call.TalkTime + 10;

                        if (reqPriorityModel.Call.Duration <=2 && reqPriorityModel.Call.CallType== "OB")
                        {
                            sb.Append("Skip < =2 duration \r\n");
                            update = Update<PriorityModel>
                                    .Set(p => p.SkippingTime, DateTime.Now)
                                    .Set(p => p.SkipDurationHrs, 90);

                        }
                        else if (IsSkipLead(reqPriorityModel, respPriorityModel) == true)
                        {
                            sb.Append("Skip-ELSEIF \r\n");
                            update = Update<PriorityModel>
                                    .Set(p => p.SkippingTime, DateTime.Now)
                                    .Set(p => p.SkipDurationHrs, 5);

                        }
                        else if ((respPriorityModel != null && respPriorityModel.Call == null) || (reqPriorityModel.Call != null && DateTime.Now.Date == reqPriorityModel.Call.calltime.Date && respPriorityModel.Call.calltime != reqPriorityModel.Call.calltime && (respPriorityModel.Call.uid != reqPriorityModel.Call.uid || respPriorityModel.Call.IsProcessed == false)))
                        {
                            sb.Append("inside \r\n");
                            CallData _CallData = new CallData();
                            if (reqPriorityModel.Call == null)
                                return;

                            sb.Append("start \r\n");
                            SetCallData(_CallData, reqPriorityModel, respPriorityModel);
                            //skip same customer health lead
                            CheckAddonCalling(respPriorityModel);

                            //update today answered attempts
                            if (TodayFirstCall(respPriorityModel, reqPriorityModel))
                            {
                                ValidUnasnweredAttempt = 0;                                                            
                                if (IsValidNANC(reqPriorityModel))
                                {
                                    ValidUnasnweredAttempt = 1;
                                    _CallData.TodaysNANCAttempt = 1;
                                    _CallData.LastNminuteNANCeAttempts = 1;
                                }
                            }
                            else if (respPriorityModel.Call != null)
                            {
                                if (reqPriorityModel.Call.TalkTime > 0)
                                {
                                    _CallData.TodayAnswered = Convert.ToInt16(respPriorityModel.Call.TodayAnswered + 1);                                    
                                }

                                if (IsValidNANC(reqPriorityModel))
                                {
                                    ValidUnasnweredAttempt = 1;
                                    _CallData.TodaysNANCAttempt = Convert.ToInt16(respPriorityModel.Call.TodaysNANCAttempt + 1);// updating today's NANCattempt                                                                              

                                    if (respPriorityModel.Call.LastNminuteNANCeAttempts == 0)
                                        _CallData.LastNminuteNANCeAttempts = 1;
                                    else if (reqPriorityModel.Call.LastCallTime != DateTime.MinValue && (reqPriorityModel.Call.calltime - reqPriorityModel.Call.LastCallTime).TotalMinutes < 30)
                                        _CallData.LastNminuteNANCeAttempts = Convert.ToInt16(respPriorityModel.Call.LastNminuteNANCeAttempts + 1);
                                    else if (respPriorityModel.Call.LastNminuteNANCeAttempts >= 2)
                                        _CallData.LastNminuteNANCeAttempts = Convert.ToInt16(respPriorityModel.Call.LastNminuteNANCeAttempts + 1);
                                }
                                else
                                {
                                    ValidUnasnweredAttempt = 0;
                                    if (reqPriorityModel.Call.TalkTime >= 15)
                                        _CallData.LastNminuteNANCeAttempts = 0;
                                }
                            }                            
                            // weekly attempts
                            try
                            {
                                if (IsValidNANC(reqPriorityModel))
                                {
                                    Int16 currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(reqPriorityModel.Call.calltime.Date.Subtract(Convert.ToDateTime(respPriorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1);
                                    if (respPriorityModel.Call == null || (respPriorityModel.Call != null && currentWeek != respPriorityModel.Call.Current_Week))
                                    {
                                        _CallData.Week_Attempt = 1;
                                        _CallData.Current_Week= currentWeek;
                                        WeekAttempts = 1;
                                    }
                                    else
                                    {
                                        if (respPriorityModel.Call != null)
                                        {
                                            _CallData.Week_Attempt = Convert.ToInt16(respPriorityModel.Call.Week_Attempt + 1);
                                            _CallData.Current_Week = Convert.ToInt16(currentWeek);
                                            WeekAttempts = Convert.ToInt16(respPriorityModel.Call.Week_Attempt + 1);
                                        }
                                        else
                                        {
                                            _CallData.Week_Attempt = 1;
                                            _CallData.Current_Week= currentWeek;
                                            WeekAttempts = 1;
                                        }
                                    }
                                }
                                else if (respPriorityModel.Call != null && reqPriorityModel.Call.TalkTime >= 240)
                                {
                                    CallData _CallDataweeks = LeadPrioritizationDLL.SetAnsWeekAttempts(respPriorityModel);

                                    _CallData.Current_Week = _CallDataweeks.Current_Week;
                                    _CallData.Week_Attempt= _CallDataweeks.Week_Attempt;

                                    WeekAttempts = _CallDataweeks.Week_Attempt;
                                }
                            }
                            catch (Exception ex)
                            {
                                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(respPriorityModel.LeadID), 0, ex.ToString(), "WeekAttempts", "LeadPrioritizationBLL", "Communication", JsonConvert.SerializeObject(respPriorityModel), string.Empty, DateTime.Now, DateTime.Now);
                            }


                            if (reqPriorityModel.Call.TalkTime == 0 && reqPriorityModel.Call.Duration > 0)
                            {
                                //update = update.Set(p => p.Call.NANC_Attempts, respPriorityModel.Call == null ? 1 : respPriorityModel.Call.NANC_Attempts + 1);// updating the number
                                UpdateNANC_ShiftSlot(_CallData, respPriorityModel, reqPriorityModel); // This will handle shifting of                                
                            }
                            update = Update<PriorityModel>.Set(p => p.Call, _CallData);
                            //update call release count
                            if (respPriorityModel.CallReleaseCount > 0 && reqPriorityModel.Call.Duration > 2)
                            {
                                update = update.Set(p => p.CallReleaseCount, 0);
                            }
                        }
                        try{
                            // Update latestIBcalltime
                            if (reqPriorityModel.Call != null && (reqPriorityModel.Call.CallType == "IB" || reqPriorityModel.Call.CallType == "CTC") && respPriorityModel != null && respPriorityModel.DNC != null)
                            {
                                if (update == null) {
                                    update = Update<PriorityModel>.Set(p => p.DNC.LastIbCallTime, DateTime.Now);
                                }
                                else {
                                    update = update.Set(p => p.DNC.LastIbCallTime, DateTime.Now);
                                }
                            }
                        }
                        catch{}

                        //Get All customer Leads
                        if (ValidUnasnweredAttempt > -1)
                        {
                            SetNANCCustAttempt(respPriorityModel, ValidUnasnweredAttempt, respPriorityModel.CustID, reqPriorityModel.LeadID, objCommDB);
                        }
                        break;
                    case EventTypeEnum.Assignment:// will be treated as reassignment    
                        if (reqPriorityModel.User.UserID == respPriorityModel.User.UserID)
                            return;

                        update = Update<PriorityModel>
                                .Set(p => p.ts, DateTime.Now)
                                .Set(p => p.User.Reassigned, reqPriorityModel.User.Reassigned)
                                .Set(p => p.User.AssignedOn, reqPriorityModel.User.AssignedOn)
                                .Set(p => p.User.GroupId, reqPriorityModel.User.GroupId)
                                .Set(p => p.User.UserID, reqPriorityModel.User.UserID)
                                .Set(p => p.User.Grade, reqPriorityModel.User.Grade)
                                .Set(p => p.User.JobID, reqPriorityModel.User.JobID)
                                .Set(p => p.LeadRank, reqPriorityModel.LeadRank)
                                .Set(p => p.Appointment, reqPriorityModel.Appointment);

                        //set  call attempts to 0 in case of reassignment
                        /*if (reqPriorityModel.User.Reassigned && reqPriorityModel.Call != null && reqPriorityModel.Call.CallAttempts > 0)
                        {
                            update = update.Set(p => p.Call.CallAttempts, 0)
                                .Set(p => p.Call.NANC_Attempts, 0);
                        }*/

                        //LeadPrioritizationDLL.TrackAssignAndStatusIDs(reqPriorityModel.User.AssignedID, "ASSIGNMENT");

                        break;
                    case EventTypeEnum.statusupdate:
                        LeadPrioritizationDLL.UpdateLeadStatusData(ref update, respPriorityModel, reqPriorityModel);
                        break;

                    case EventTypeEnum.SingleNA1Hr:
                        {
                            update = Update<PriorityModel>
                                     .Set(x => x.Call.NANC_Attempts, respPriorityModel.Call.NANC_Attempts + 1);                            
                        }
                        break;
                    case EventTypeEnum.SkipLead:
                        {
                            update = Update<PriorityModel>
                                     .Set(x => x.SkippingTime, DateTime.Now);
                        }
                        break;
                    case EventTypeEnum.selection:
                        {
                            update = Update<PriorityModel>.Set(x => x.IsSelection, true);
                        }
                        break;
                    case EventTypeEnum.ReleaseLeads:
                        {
                            update = Update<PriorityModel>.Set(x => x.ReleaseStatus, reqPriorityModel.ReleaseStatus);
                            if (respPriorityModel.Call != null && reqPriorityModel.ReleaseStatus == 2)
                            {

                                Int16 Current_Week = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(DateTime.Now.Date.Subtract(respPriorityModel.User.FirstAssignedOn).TotalDays) / 7) + 1);
                                WeekAttempts = respPriorityModel.Call.Current_Week == 1 ? (respPriorityModel.Call.Week_Attempt > (_ProductWiseCOnstant.Week1MaxAttempts - 3) ? Convert.ToInt16(_ProductWiseCOnstant.Week1MaxAttempts - 3) : respPriorityModel.Call.Week_Attempt) : (respPriorityModel.Call.Week_Attempt > (_ProductWiseCOnstant.WeekMaxAttempts - 3) ? Convert.ToInt16(_ProductWiseCOnstant.WeekMaxAttempts - 3) : respPriorityModel.Call.Week_Attempt);

                                update = update.Set(x => x.CallReleaseCount, oLeadPriorityConstants.CallReleaseCount)
                                                              .Set(x => x.CallReleaseTime, DateTime.Now)
                                                              .Set(x => x.Call.TodaysNANCAttempt, respPriorityModel.Call.calltime.Date == DateTime.Now.Date && respPriorityModel.Call.TodaysNANCAttempt > 3 ? 2 : respPriorityModel.Call.TodaysNANCAttempt) // valid unanswered attempts not to picked (reset incase of revisit) 
                                                              .Set(x => x.Call.Current_Week, Current_Week)
                                                              .Set(x => x.Call.Week_Attempt, WeekAttempts)
                                                              .Set(x => x.CustomerNANCAttempt, respPriorityModel.CustomerCalltime.Date == DateTime.Now.Date && respPriorityModel.CustomerNANCAttempt > 3 ? 2 : respPriorityModel.CustomerNANCAttempt); // valid unanswered attempts not to picked (reset incase of revisit) 
                            }
                        }
                        break;
                }
                str.Append("EventType Check End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                if (query != null && update != null)// update PriorirtyModel Mongo
                {
                    str.Append("UpdatePriorityModel Start " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                    objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                    str.Append("UpdatePriorityModel End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");

                    sb.Append("Update Query - " + update.ToString());
                }

                if (reqPriorityModel.EventType != EventTypeEnum.Assignment)// For First Assignment LeadPoint Method already Called Above
                {
                    str.Append("UpdateLeadPoint Start " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                    UpdateLeadPoint(reqPriorityModel, respPriorityModel, WeekAttempts);
                    str.Append("UpdateLeadPoint End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                }
                else
                {
                    LeadPrioritizationDLL.InsertUpdateRevisitPageInfo(string.Empty, reqPriorityModel.LeadID, reqPriorityModel.User.UserID, false, "", reqPriorityModel.LeadID, 0, DateTime.Now);// when lead is reassigned UserId will be updated with new UserId
                    reqPriorityModel.LeadPointOperation = true;
                }
                reqPriorityModel.MongoOperation = true;
                //if ((reqPriorityModel.MongoOperation == false || reqPriorityModel.LeadPointOperation == false) && ((DateTime.Now - reqPriorityModel.ts).TotalMinutes < oLeadPriorityConstants.PriorityeventQExpMinutes))
                //{
                //    AddLPDataPointInQueue(reqPriorityModel);
                //    sb.Append("Add in queue \r\n");
                //}
            }

            // Update NANC against CustomerID in a separate Collection
            if (reqPriorityModel != null && reqPriorityModel.EventType == EventTypeEnum.Call) {
                UpdateCustomerNANC(reqPriorityModel, respPriorityModel!= null && respPriorityModel.CustID > 0? respPriorityModel.CustID: 0);
            }

        }
        catch (Exception ex)
        {
            strexception = ex.ToString();
            //LoggingHelper.LoggingHelper.Log("", reqPriorityModel.LeadID, strexception, "UpdatePriorityModelNLeadPoint_error", "UpdatePriorityModelNLeadPoint_error", "Automation", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
            //if ((DateTime.Now - reqPriorityModel.ts).TotalMinutes < oLeadPriorityConstants.PriorityeventQExpMinutes)
            //   AddLPDataPointInQueue(reqPriorityModel);
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", reqPriorityModel.LeadID, strexception, "UpdatePriorityModelNLeadPoint", "LeadPrioritizationBLL", "Automation", sb.ToString(), "UpdatePriorityModelNLeadPointTimeSplit" + str.ToString(), RequestDatetime, DateTime.Now);
        }
    }

    private bool TodayFirstCall(PriorityModel respPriorityModel, PriorityModel reqPriorityModel)
    {
        if (respPriorityModel.Call == null || reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date)
            return true;
        else
            return false;

    }

    public static bool IsSkipLead(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
    {
        int minPointDeductTime = Convert.ToInt16("minPointDeductTime".AppSettings());
        if (                    (_respPriorityModel.Call == null || (_respPriorityModel.Call.calltime != _respPriorityModel.Call.calltime))
                            && _reqPriorityModel.Call.Duration <= minPointDeductTime
                            && _reqPriorityModel.Call.Disposition != null
                            && (_reqPriorityModel.Call.Duration == 0 || _respPriorityModel.SkippingTime == DateTime.MinValue || _respPriorityModel.SkippingTime.Date < DateTime.Now.Date || DateTime.Now.Subtract(_respPriorityModel.SkippingTime).TotalMinutes > 60)
                            )
            return true;
        else
            return false;
    }

    void SetCallData(CallData _CallData, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
    {
        if (reqPriorityModel == null || reqPriorityModel.Call == null || respPriorityModel==null)
            return;
        _CallData.calltime = reqPriorityModel.Call.calltime;
        _CallData.Disposition = reqPriorityModel.Call.Disposition;
        _CallData.Duration = reqPriorityModel.Call.Duration;
        _CallData.TalkTime = reqPriorityModel.Call.TalkTime;
        _CallData.TotalTT = (respPriorityModel.Call != null && respPriorityModel.Call.TotalTT > reqPriorityModel.Call.TotalTT ? respPriorityModel.Call.TotalTT : reqPriorityModel.Call.TotalTT);
        _CallData.CallAttempts = respPriorityModel.Call == null ? Convert.ToInt16(1) : Convert.ToInt16(respPriorityModel.Call.CallAttempts + 1);
        _CallData.uid = reqPriorityModel.Call.uid;
        _CallData.CallType = reqPriorityModel.Call.CallType;
        _CallData.IsProcessed = true;
        _CallData.TodayTalktime = reqPriorityModel.Call.TodayTalktime;
        _CallData.lastNCallTT = reqPriorityModel.Call.lastNCallTT;
        _CallData.NANC_Attempts = reqPriorityModel.Call.NANC_Attempts;
        _CallData.TodayAnswered = reqPriorityModel.Call.TalkTime > 0 ? Convert.ToInt16(1) : Convert.ToInt16(0);
        _CallData.TodaysAttempt = reqPriorityModel.Call.Duration > 2 ? Convert.ToInt16(1) : Convert.ToInt16(0);
        _CallData.CallingNo = reqPriorityModel.Call.CallingNo == 0 && respPriorityModel.Call != null && respPriorityModel.Call.CallingNo > 0 ? respPriorityModel.Call.CallingNo : reqPriorityModel.Call.CallingNo;
        _CallData.LastConnectedCall = reqPriorityModel.Call.TalkTime > 0 ? reqPriorityModel.Call.calltime : reqPriorityModel.Call != null ? reqPriorityModel.Call.LastConnectedCall:DateTime.MinValue;
    }

    public static void UpdateLeadPoint(PriorityModel reqPriorityModel, PriorityModel? respPriorityModel, Int16 WeekAttempts)
    {
        try
        {
            Int16 currentWeek = respPriorityModel == null ? Convert.ToInt16(0) : Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(DateTime.Now.Date.Subtract(respPriorityModel.User.FirstAssignedOn.Date).TotalDays) / 7) + 1);
            DateTime FirstAssignedOn = (reqPriorityModel.EventType == EventTypeEnum.Assignment)
                        ? reqPriorityModel.User.FirstAssignedOn
                        : (respPriorityModel != null && respPriorityModel.User != null ? respPriorityModel.User.FirstAssignedOn : DateTime.Now);

            LeadPointsDTO oLeadPointsDTO = new LeadPointsDTO()
            {
                LeadId = reqPriorityModel.LeadID,
                PointType = Convert.ToByte(reqPriorityModel.EventType),
                Points = Convert.ToInt16(0),
                FirstAssignedOn = FirstAssignedOn,
                CurrentWeek = currentWeek,
                WeekPoints = WeekAttempts
            };

            LeadPrioritizationDLL.InsUpLeadPointsInSQL(oLeadPointsDTO);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", reqPriorityModel.LeadID, ex.ToString(), "UpdateLeadPoint", "OneLeadPriority", "LeadPrioritizationBLL", "", string.Empty, DateTime.Now, DateTime.Now);
        }
    }

    private static void CheckAddonCalling(PriorityModel respPriorityModel)
    {
        try
        {
            List<Int16> healthProducts = new List<short>() { 2, 106, 118, 130 };

            if (healthProducts.Contains(respPriorityModel.ProductID))
            {
                List<PriorityModel> CustrespPriorityModel = LeadPrioritizationDLL.GetAgentPriorityLeads_Customer(respPriorityModel.CustID, respPriorityModel.User.UserID);// Mongo StoredObject
                if (CustrespPriorityModel.Count > 1 && CustrespPriorityModel.Count < 15)
                {
                    foreach (var custmodel in CustrespPriorityModel)
                    {
                        if (respPriorityModel.LeadID != custmodel.LeadID)
                        {
                            LeadPrioritizationDLL.SkipLead(custmodel.LeadID, 1442);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", respPriorityModel.LeadID, ex.ToString(), "CheckAddonCalling", "CommAPI", "LeadPrioritizationBLL", JsonConvert.SerializeObject(respPriorityModel), "", DateTime.Now, DateTime.Now);
        }
    }
    private void getCustNANCAttempts(long CustID, long CurrentLead, ref Int16 CustomerNANCAttempt, ref Int16 CustomerTodayNANC, ref Int16 CustrespCount, ref DateTime CustomerCallTime)
    {
        List<PriorityModel> CustrespPriorityModel = LeadPrioritizationDLL.GetPriorityLeads_Customer(CustID);
        CustrespCount = Convert.ToInt16(CustrespPriorityModel.Count);
        if (CustrespCount > 1 && CustrespCount < 20)
        {
            foreach (var custmodel in CustrespPriorityModel)
            {
                if (CurrentLead != custmodel.LeadID)
                {

                    if (custmodel.CustomerCalltime != DateTime.MinValue && custmodel.CustomerCalltime.Date == DateTime.Now.Date)
                    {
                        CustomerNANCAttempt = CustomerNANCAttempt > custmodel.CustomerNANCAttempt ? CustomerNANCAttempt : custmodel.CustomerNANCAttempt;
                        CustomerTodayNANC = CustomerTodayNANC > custmodel.CustomerTodayNANC ? CustomerTodayNANC : custmodel.CustomerTodayNANC;
                        CustomerCallTime = custmodel.CustomerCalltime;
                        break;
                    }
                }
            }
        }

    }
    private void SetNANCCustAttempt(PriorityModel respPriorityModel, Int16 ValidUnasnweredAttempt, long CustID, long CurrentLead, MongoHelper objCommDB)
    {
        try
        {
            UpdateBuilder<PriorityModel> update = null;
            IMongoQuery query = Query.And(Query<PriorityModel>.EQ(p => p.CustID, CustID), Query<PriorityModel>.EQ(p => p.IsActive, true));
            Int16 CustomerNANCAttempt = 0;
            Int16 CustomerTodayNANC = 0;
            Int16 CustrespCount = 0;
            DateTime CustomerCallTime = DateTime.MinValue;
            if (respPriorityModel != null && respPriorityModel.CustomerCalltime != DateTime.MinValue && respPriorityModel.CustomerCalltime.Date == DateTime.Now.Date)
            {
                CustomerNANCAttempt = respPriorityModel.CustomerNANCAttempt;
                CustomerTodayNANC = respPriorityModel.CustomerTodayNANC;
            }

            getCustNANCAttempts(CustID, CurrentLead, ref CustomerNANCAttempt, ref CustomerTodayNANC, ref CustrespCount, ref CustomerCallTime);


            update = Update<PriorityModel>
                            .Set(p => p.CustomerNANCAttempt, CustomerNANCAttempt + ValidUnasnweredAttempt)
                            .Set(p => p.CustomerTodayNANC, CustomerTodayNANC + ValidUnasnweredAttempt)
                            .Set(p => p.CustomerCalltime, DateTime.Now);

            if (CustrespCount >= 200)
            {
                // Quering on leadid incase of active records are more
                query = Query<PriorityModel>.EQ(p => p.LeadID, CurrentLead);
                LoggingHelper.LoggingHelper.AddloginQueue("", CurrentLead, string.Empty, "SetNANCCustAttempt", "CommAPI", "LeadPrioritizationBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection(), UpdateFlags.Multi);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", CurrentLead, ex.ToString(), "SetNANCCustAttempt-Error", "CommAPI", "LeadPrioritizationBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
        }
    }

    // This method is for Updating the call shift
    private static void UpdateNANC_ShiftSlot(CallData _CallData, PriorityModel respPriorityModel, PriorityModel reqPriorityModel)
    {
        try
        {
            if (respPriorityModel.Call != null && respPriorityModel.Call.TotalTT == 0 && respPriorityModel.Call != null)// means no call answered till date
            {
                List<string> ExistingShifts = respPriorityModel.Call.Shifts;
                List<string> UpdateShiftsList = new List<string>();
                string LastcallShift = LeadPrioritizationDLL.CalculateCallShift(respPriorityModel.Call.calltime);
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(reqPriorityModel.Call.calltime);

                if (ExistingShifts == null)
                {
                    UpdateShiftsList.Add(CurrentCallShift);
                    _CallData.Shifts= UpdateShiftsList;
                }
                else if (!ExistingShifts.Contains(CurrentCallShift))
                {
                    ExistingShifts.Add(CurrentCallShift);
                    _CallData.Shifts= ExistingShifts;
                }
            }
        }
        catch (Exception)
        {

        }
    }

    public static UpdateBuilder<PriorityModel>? updateRevist(PriorityModel _respPriorityModel, PriorityModel _reqPriorityModel, ref Int16 WeekAttempts)
    {
        UpdateBuilder<PriorityModel>? update = null;
        LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
        var _ProductWiseCOnstant = LeadPrioritizationDLL.getPriorityConfigByProduct(_reqPriorityModel.ProductID);

        if (_reqPriorityModel.Revisit.RevisitType == RevisitType.WebVisit)
        {
            List<long> leadset = _respPriorityModel.ActiveLeadSet;
            if (_respPriorityModel.ActiveLeadSet != null && !_respPriorityModel.ActiveLeadSet.Contains(_reqPriorityModel.ActiveLeadSet[0])
                    && _LeadPriorityConstants.MatrixStatus.Contains(_reqPriorityModel.LeadStatus.StatusID))
            {
                leadset.Add(_reqPriorityModel.ActiveLeadSet[0]);
            }
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.ActiveLeadSet, leadset)
                    .Set(p => p.IsLastBucketRest, false)
                    .Set(p => p.PageName, _reqPriorityModel.PageName)
                    .Set(p => p.RevisitCount, _respPriorityModel.RevisitCount + 1);

        }
        else if (_reqPriorityModel.Revisit.RevisitType == RevisitType.Ctc)
        {
            List<long> leadset = _respPriorityModel.ActiveLeadSet;
            if (_respPriorityModel.ActiveLeadSet != null && !_respPriorityModel.ActiveLeadSet.Contains(_reqPriorityModel.ActiveLeadSet[0])
                    && _LeadPriorityConstants.MatrixStatus.Contains(_reqPriorityModel.LeadStatus.StatusID))
            {
                leadset.Add(_reqPriorityModel.ActiveLeadSet[0]);
            }
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.ActiveLeadSet, leadset)
                    .Set(p => p.IsLastBucketRest, false)
                    .Set(p => p.PageName, _reqPriorityModel.PageName)
                    .Unset(p => p.DNC);

        }
        else if (_reqPriorityModel.Revisit.RevisitType == RevisitType.Email || _reqPriorityModel.Revisit.RevisitType == RevisitType.QuoteShared)
        {
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.IsLastBucketRest, false);
            if (_reqPriorityModel.Revisit.RevisitType == RevisitType.QuoteShared)
            {
                LeadPrioritizationDLL.UpdateQuoteShared(_respPriorityModel.LeadID, _respPriorityModel.User.UserID, _respPriorityModel.CustName);
            }
            else
            {
                LeadPrioritizationDLL.InsertUpdateRevisitPageInfo("Email Revisit", _respPriorityModel.LeadID, 0, true, "", _respPriorityModel.LeadID, 0, DateTime.Now);// FOR nEW COLLECTION
            }
        }
        else if (_reqPriorityModel.Revisit.RevisitType == RevisitType.BajajCustomer)
        {
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.IsLastBucketRest, false);
        }
        else if (_reqPriorityModel.Revisit.RevisitType == RevisitType.Inbound)
        {
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.IsLastBucketRest, false)
                    .Unset(p => p.DNC);

            if (_reqPriorityModel.Call.TalkTime > 0)
            {
                update = Update<PriorityModel>
                    .Set(p => p.Call.calltime, _reqPriorityModel.Call.calltime)
                    .Set(p => p.Call.TalkTime, _reqPriorityModel.Call.TalkTime)
                    .Set(p => p.Call.TotalTT, _reqPriorityModel.Call.TotalTT)
                    .Set(p => p.Call.lastNCallTT, _reqPriorityModel.Call.lastNCallTT)
                    .Set(p => p.Call.Disposition, _reqPriorityModel.Call.Disposition)
                    .Set(p => p.Call.CallAttempts, _respPriorityModel.Call == null ? 1 : _respPriorityModel.Call.CallAttempts + 1)
                    .Set(p => p.Call.CallType, _reqPriorityModel.Call.CallType)
                    .Set(p => p.Call.TodayTalktime, _reqPriorityModel.Call.TodayTalktime);
            }
        }

        else if (_reqPriorityModel.Revisit.RevisitType == RevisitType.RevisionShared)
        {
            update = Update<PriorityModel>
                    .Set(p => p.Revisit.ts, _reqPriorityModel.Revisit.ts)
                    .Set(p => p.Revisit.RevisitType, _reqPriorityModel.Revisit.RevisitType)
                    .Set(p => p.IsLastBucketRest, false);

            LeadPrioritizationDLL.UpdateQuoteShared(_respPriorityModel.LeadID, _respPriorityModel.User.UserID, _respPriorityModel.CustName);
        }

        if (_respPriorityModel.Call != null && update != null)
        {
            CallData _CallData = LeadPrioritizationDLL.SetWeekAttempts(_respPriorityModel);

            update = update.Set(x => x.Call.TodaysNANCAttempt, _CallData.TodaysNANCAttempt)
                            .Set(x => x.Call.Current_Week, _CallData.Current_Week)
                            .Set(p => p.Call.Week_Attempt, _CallData.Week_Attempt)
                            .Set(p => p.CustomerNANCAttempt, _CallData.TodaysNANCAttempt);

            WeekAttempts = _CallData.Week_Attempt;
        }

        if (_respPriorityModel.Call != null && _respPriorityModel.Call.Shifts != null && update != null)
            update = update.Set(p => p.Call.Shifts, null);

        return update;
    }

    private static UpdateBuilder<PriorityModel> UpdateCustomerAttempts(PriorityModel reqPriorityModel, PriorityModel respPriorityModel, short ValidUnasnweredAttempt, UpdateBuilder<PriorityModel> update)
    {
        if (ValidUnasnweredAttempt > -1)
        {
            DateTime RequestDatetime = DateTime.Now;

            try
            {
                //Get All customer Leads 
                //SetNANCCustAttempt(ValidUnasnweredAttempt, respPriorityModel.CustID, reqPriorityModel.LeadID);


                if (respPriorityModel.CustomerCalltime == DateTime.MinValue || respPriorityModel.CustomerCalltime.Date < DateTime.Now.Date)
                    update = update.Set(p => p.CustomerNANCAttempt, ValidUnasnweredAttempt)
                             .Set(p => p.CustomerTodayNANC, ValidUnasnweredAttempt)
                             .Set(p => p.CustomerCalltime, DateTime.Now);
                else
                    update = update.Set(p => p.CustomerNANCAttempt, respPriorityModel.CustomerNANCAttempt + ValidUnasnweredAttempt)
                                    .Set(p => p.CustomerTodayNANC, respPriorityModel.CustomerTodayNANC + ValidUnasnweredAttempt)
                                    .Set(p => p.CustomerCalltime, DateTime.Now);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", reqPriorityModel.LeadID, ex.ToString(), "setcustomerNANC", "CommAPI", "LeadPrioritizationBLL", string.Empty, string.Empty, RequestDatetime, DateTime.Now);
            }
        }

        return update;
    }

    public string GetDNCDetails(Int64 LeadID)
    {
        string result = string.Empty;
        try
        {
            PriorityModel res = GetPriorityModelMongo(LeadID);
            if (res != null && res.DNC != null)
            {
                result = JsonConvert.SerializeObject(res.DNC);
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "GetDNCDetails", "LeadPrioritizationBLL", "OneLead", "", string.Empty, DateTime.Now, DateTime.Now);
        }
        return result;
    }

    public PriorityModel GetPriorityModelMongo(Int64 LeadId)
    {
        PriorityModel oPriorityModel = new PriorityModel();
        oPriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(LeadId);
        return oPriorityModel;
    }
    public bool PushCallDatainQueue(string CallID, string LeadID, string AgentCode, string Duration, string talkTime, string status, string callDate, string callType, string Context, string disposition, string mtxCallID, string dst, string channel, string action, string hanguptime, string IP, string callRequestType, string IsBMS, string recfile, string t_type, string CallingNo, string rectype)
    {
        DialerDispDetails _DispositionUpdate = new DialerDispDetails();
        string json = string.Empty;
        string exception = string.Empty;
        DateTime reqdt = DateTime.Now;
        AmazonSqs amazonSqs = new AmazonSqs();
        try
        {
            int requestType = 0;
            int callduration = 0;
            if (!string.IsNullOrEmpty(Duration))
                callduration = Convert.ToInt32(Duration);

            if (callduration < 0)
                callduration = 0;

            if (!string.IsNullOrEmpty(LeadID) && Convert.ToInt64(LeadID) > 0)
                _DispositionUpdate.LeadID = Convert.ToInt64(LeadID);

            _DispositionUpdate.Duration = callduration;
            _DispositionUpdate.AsteriskIP = IP;
            if (!string.IsNullOrEmpty(talkTime))
            {
                _DispositionUpdate.talktime = Convert.ToInt32(talkTime);
                if (callduration == 0 && _DispositionUpdate.talktime > 0)
                {
                    _DispositionUpdate.Duration = _DispositionUpdate.talktime;
                }
            }
            _DispositionUpdate.CallId = CallID;
            _DispositionUpdate.CreatedOn = DateTime.Now;
            _DispositionUpdate.Status = status;
            _DispositionUpdate.callDate = string.IsNullOrEmpty(callDate) ? DateTime.Now.AddSeconds(-callduration) : Convert.ToDateTime(callDate);
            _DispositionUpdate.CallType = callType;
            _DispositionUpdate.AgentCode = AgentCode;
            _DispositionUpdate.Context = Context;
            _DispositionUpdate.Disposition = disposition;
            if (_DispositionUpdate.CallType != null && _DispositionUpdate.CallType.Equals("C2C"))
            {
                _DispositionUpdate.C2CID = Convert.ToInt32(mtxCallID);
            }
            else
                _DispositionUpdate.CallTrackingID = mtxCallID;
            _DispositionUpdate.NewMethod = true;


            int.TryParse(callRequestType, out requestType);
            _DispositionUpdate.IsService = requestType;
            if (!string.IsNullOrEmpty(hanguptime))
                _DispositionUpdate.HangUpTime = Convert.ToDateTime(hanguptime);
            else
                _DispositionUpdate.HangUpTime = DateTime.Now;
            _DispositionUpdate.Channel = channel;
            _DispositionUpdate.dst = dst;
            _DispositionUpdate.Action = action;
            if (IsBMS == "1")
                _DispositionUpdate.IsBMS = true;

            _DispositionUpdate.recfile = recfile;
            _DispositionUpdate.t_type = t_type;
            _DispositionUpdate.CallingNo = (!string.IsNullOrEmpty(CallingNo)) ? Convert.ToInt64(CallingNo) : Convert.ToInt64(0);
            _DispositionUpdate.rectype = string.IsNullOrEmpty(rectype) ? "dual" : "single";

            json = Newtonsoft.Json.JsonConvert.SerializeObject(_DispositionUpdate);
            string dialerdataQueueName = "dialerdataqueue".AppSettings();
            if ("AmazonSqsActive".AppSettings() == "1")
            {
                string URL = "AmazonSqsURL".AppSettings();
                amazonSqs.SQSSendMessage(URL, _DispositionUpdate);
            }
            else
            {
                if (_DispositionUpdate.Action != "agentanswered")
                {
                    _DispositionUpdate.TryCount = Convert.ToInt16(_DispositionUpdate.TryCount + 1);

                    if (!string.IsNullOrEmpty(_DispositionUpdate.CallingTime))
                    {
                        _DispositionUpdate.callDate = Convert.ToDateTime(_DispositionUpdate.CallingTime);
                    }
                }
                InsertUpdateDialerData(_DispositionUpdate);
                if (_DispositionUpdate.Action != "agentanswered")
                {
                    InsertCallDatafromDialerQueue(_DispositionUpdate);
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            exception = ex.ToString();
            return false;
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(LeadID, Convert.ToInt64(LeadID), exception, "PushCallDatainQueue", "LeadPrioritizationBLL", "124", json, string.Empty, reqdt, DateTime.Now);
        }
    }
    public void InsertUpdateDialerData(DialerDispDetails _DispositionUpdate)
    {
        DateTime requesttime = DateTime.Now;
        string exception = string.Empty;
        string strResponse = string.Empty;

        try
        {
            if (!string.IsNullOrEmpty(_DispositionUpdate.Action))
            {
                if (_DispositionUpdate.Action == "agentanswered")
                {
                    var success = InsertUpdateCallData.UpdateDataonAgentAnswered(_DispositionUpdate, ref strResponse);
                }
                else if (_DispositionUpdate.Action == "hangup")
                {
                    var success = InsertUpdateCallData.HangUpOBCall(_DispositionUpdate);
                }
            }
        }
        catch (Exception ex)
        {
            exception = ex.ToString();
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(_DispositionUpdate.LeadID), _DispositionUpdate.LeadID, exception, "InsertUpdateDialerData", "LeadPrioritizationBLL", string.Empty, string.Empty, strResponse, requesttime, DateTime.Now);
        }
    }
    bool IsValidSalesLead(DialerDispDetails _DispositionUpdate)
    {
        if (_DispositionUpdate.IsBMS == false && _DispositionUpdate.LeadID > 0 && _DispositionUpdate.LeadID != 111)
            return true;
        else
            return false;
    }
    bool IsServiceCall(DialerDispDetails _DispositionUpdate)
    {
        if ((!string.IsNullOrEmpty(_DispositionUpdate.AgentCode) && _DispositionUpdate.AgentCode.StartsWith("B"))
                    || _DispositionUpdate.IsBMS == true
            )
            return true;
        else
            return false;
    }
    public bool InsertCallDatafromDialerQueue(DialerDispDetails _DispositionUpdate)
    {
        string Exception = string.Empty;
        string Historystatus = string.Empty;
        DateTime RequestDatetime = DateTime.Now;
        string response = string.Empty;
        AmazonSqs amazonSqs = new AmazonSqs();
        string Mobile = string.Empty;
        string Method = "InsertCallDatafromDialerQueueonelead";
        StringBuilder str = new StringBuilder();
        try
        {
            /*Get Lead ProductId ,ParentId*/
            LeadCallDetailsDTO _leaddetailsobj = new LeadCallDetailsDTO();
            if (_DispositionUpdate.IsBMS == false || _DispositionUpdate.LeadID > 0)
                _leaddetailsobj = GetLeaddetailsByCallID(_DispositionUpdate.LeadID, _DispositionUpdate.CallId);

            if (_leaddetailsobj != null)
            {
                _DispositionUpdate.ProductID = _leaddetailsobj.productId;
                _DispositionUpdate.ParentID = _leaddetailsobj.ParentId;
            }
            if ((!string.IsNullOrEmpty(_DispositionUpdate.AgentCode) && _DispositionUpdate.AgentCode.StartsWith("B")) || _DispositionUpdate.IsBMS == true)
                _DispositionUpdate.ParentID = _DispositionUpdate.LeadID;
            /**/

            /*insert call data*/
            string calldataid = LeadPrioritizationDLL.InsertCallData(_DispositionUpdate);
            if (string.IsNullOrEmpty(calldataid))
                calldataid = "0";
            if (string.IsNullOrEmpty(_DispositionUpdate.CallTrackingID))
                _DispositionUpdate.CallTrackingID = calldataid;
            /**/

            /*SaveAppHistory for tracking*/
            if (IsValidSalesLead(_DispositionUpdate) == true)
                LeadPrioritizationDLL.SaveAppHistory(_DispositionUpdate);
            /**/

            if (!string.IsNullOrEmpty(_DispositionUpdate.CallTrackingID) && _DispositionUpdate.InsertCheckFlag == 0)
            {
                if ("AmazonSqsActive".AppSettings() == "1")
                {
                    try
                    {
                        string URL = "AmazonSqsURL".AppSettings();
                        amazonSqs.SQSSendMessage(URL, _DispositionUpdate, 180);
                    }
                    catch (Exception Ex)
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.LeadID.ToString(), _DispositionUpdate.LeadID, Ex.Message, "InsertCallDatafromDialerQueue", "OneLeadPriority", string.Empty, _DispositionUpdate.Status + " - Duration " + _DispositionUpdate.Duration, response, RequestDatetime, DateTime.Now);

                    }
                }

            }

            /*Added aswat recording to queue*/
            InsertUpdateCallData.AddRecordingToQueue(_DispositionUpdate);
            /**/

            /*Get lead talk time by ParentId*/
            LeadCallDetailsDTO obj = GetLeadTalkTime(_DispositionUpdate.ParentID, _DispositionUpdate.ProductID, ref Mobile, _DispositionUpdate.CallTrackingID);
            obj.productId = _leaddetailsobj.productId;
            obj.ParentId = _leaddetailsobj.ParentId;
            obj.LeadSource = _leaddetailsobj.LeadSource;
            if (_DispositionUpdate.talktime > obj.Totaltalktime)
                obj.Totaltalktime = _DispositionUpdate.talktime;

            if (_DispositionUpdate.talktime > obj.lastNtalktime)
            {
                obj.lastNtalktime = _DispositionUpdate.talktime;
            }
            _DispositionUpdate.LeadSource = _leaddetailsobj.LeadSource;
            _DispositionUpdate.ProductID = _leaddetailsobj.productId;
            _DispositionUpdate.MobileNo = Mobile;
            Int64 userID = obj.userID;
            /**/

            //Add data in BMS SQS
            if (IsServiceCall(_DispositionUpdate) == true)
                InsertUpdateCallData.PushDatatoBMSSQS(_DispositionUpdate);
            /**/

            if (IsValidSalesLead(_DispositionUpdate) == true && !("ManualStampingProds".AppSettings().Contains("," + obj.productId + ",") && obj.LeadSource.Equals("Renewal")))
                UpdateLeadStatus(obj);

            /* call Priotization update  */
            if (IsValidSalesLead(_DispositionUpdate) == true)
            {
                response = "in Priority";
                PriorityModel _PriorityModel = new PriorityModel();
                string CallType = string.Empty;
                if (_DispositionUpdate.CallType == "PDOB" || _DispositionUpdate.CallType == "IVROB")
                    CallType = "OB";
                else
                    CallType = _DispositionUpdate.CallType;
                _PriorityModel.LeadID = _DispositionUpdate.LeadID;
                _PriorityModel.Call = new CallData
                {
                    calltime = _DispositionUpdate.callDate,
                    CallType = CallType,
                    Disposition = _DispositionUpdate.Status,
                    Duration = !string.IsNullOrEmpty(_DispositionUpdate.Context) && _DispositionUpdate.Context == "twowaycall" ? 0 : _DispositionUpdate.Duration,
                    TalkTime = _DispositionUpdate.talktime,
                    TotalTT = (obj.Totaltalktime == 0 && _DispositionUpdate.talktime > 0) ? _DispositionUpdate.talktime : obj.Totaltalktime,// some time total talktime is not calculated due to error in updation in SQL DUE TO DAILER WRONG DAILERID
                    lastNCallTT = obj.lastNtalktime,
                    uid = Convert.ToInt64(_DispositionUpdate.CallTrackingID),
                    LastCallTime = obj.LastCallTime,
                    TodayTalktime = obj.TodayTalktime,
                    NANC_Attempts = obj.NANC_Attempts,
                    CallingNo= _DispositionUpdate.CallingNo
                };

                /*
                if(CallType=="IB" && _DispositionUpdate.talktime ==0)
                {
                    _PriorityModel.EventType = EventTypeEnum.Revisit;
                    _PriorityModel.Revisit = new RevisitData { ts = DateTime.Now, RevisitType = RevisitType.Inbound };
                }
                else
                    _PriorityModel.EventType = EventTypeEnum.Call;*/

                if (CallType == "OB" || _DispositionUpdate.talktime > 0)
                    _PriorityModel.EventType = EventTypeEnum.Call;
                else
                {
                    _PriorityModel.EventType = EventTypeEnum.Revisit;
                    _PriorityModel.Revisit = new RevisitData { ts = DateTime.Now, RevisitType = RevisitType.Inbound };
                }

                str.Append("AddLPDataPointInQueue Start " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                AddLPDataPointInQueue(_PriorityModel);
            }
            /**/

            //Call BMS API 
            if (IsServiceCall(_DispositionUpdate) == true && _DispositionUpdate.LeadID > 0 && _DispositionUpdate.LeadID != 111)
            {
                if (!string.IsNullOrEmpty(Mobile) && Mobile.Length >= 10)
                {
                    Mobile = Mobile.Substring(Mobile.Length - 10, 10);
                }
                else
                    Mobile = "0";

                if (_DispositionUpdate.callDate.Date == DateTime.Now.Date)
                {
                    //str.Append("BMS API Call Start " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture) + "\r\n");
                    str.Append("BMS API Call Start " + DateTime.Now + "\r\n");
                    response = response + "_InBMSAPI";
                    string BMSURL = "BMSAPI".AppSettings() + "CallFeedbackfromCustomer";
                    dynamic dobj = new ExpandoObject();
                    dobj.ProductId = _DispositionUpdate.ProductID;
                    dobj.LeadID = _DispositionUpdate.LeadID;
                    dobj.CallTypeId = 1;
                    if (!string.IsNullOrEmpty(_DispositionUpdate.CallType))
                    {
                        switch (_DispositionUpdate.CallType.ToUpper())
                        {
                            case "OB":
                                dobj.CallTypeId = 2;
                                break;
                            case "PDOB":
                                dobj.CallTypeId = 4;
                                break;
                            case "C2C":
                                dobj.CallTypeId = 3;
                                break;
                        }
                    }
                    dobj.TalkTime = _DispositionUpdate.talktime;
                    dobj.AgentEcode = _DispositionUpdate.AgentCode;
                    if (!string.IsNullOrEmpty(_DispositionUpdate.Disposition) && _DispositionUpdate.Disposition.ToUpper() == "OFFHOUR")
                        dobj.IsWorkingHours = false;
                    else
                        dobj.IsWorkingHours = true;
                    dobj.MobileNo = Mobile;
                    dobj.CommunicationType = 8;
                    dobj.CallUId = _DispositionUpdate.CallId;
                    dobj.Disposition = _DispositionUpdate.Disposition;
                    dobj.IsTransferred = !string.IsNullOrEmpty(_DispositionUpdate.t_type) && _DispositionUpdate.t_type == "INITIATED" ? 1 : 0;
                    Dictionary<string, string> ObjHeaders = new Dictionary<string, string>();
                    ObjHeaders.Add("TOKEN", "BMSAPIToken".AppSettings().ToString());
                    ObjHeaders.Add("REQUESTINGSYSTEM", "Dialer");
                    CommonAPICall.PostAPICall(BMSURL, 1000, Newtonsoft.Json.JsonConvert.SerializeObject(dobj), string.Empty, ObjHeaders);
                    //response = response + "-" + result;
                }
                else
                    response = response + "_InBMSAPI-NotTodayDate";

            }

            return true;
        }
        catch (Exception ex)
        {
            Exception = ex.ToString();
            Method = Method + "-EX";
            return false;
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.LeadID.ToString(), _DispositionUpdate.LeadID, Exception, Method, "OneLeadPriority", "TimeSplit" + str.ToString(), _DispositionUpdate.Status + " - Duration " + _DispositionUpdate.Duration, response, RequestDatetime, DateTime.Now);
        }
    }
    public LeadCallDetailsDTO GetLeaddetailsByCallID(long leadID, string callID)
    {
        LeadCallDetailsDTO obj = new LeadCallDetailsDTO();
        DataSet ds = LeadPrioritizationDLL.GetLeaddetailsByCallIDData(leadID, callID);
        if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
        {
            if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ParentID"].ToString()))
                obj.ParentId = Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]);
            if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ProductID"].ToString()))
                obj.productId = Convert.ToInt32(ds.Tables[0].Rows[0]["ProductID"]);
            if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["LeadSource"].ToString()))
                obj.LeadSource = Convert.ToString(ds.Tables[0].Rows[0]["LeadSource"]);
        }
        return obj;
    }
    public LeadCallDetailsDTO GetLeadTalkTime(long leadId, int ProductID, ref string MobileNo, string CallDataID)
    {
        DateTime _RequestDatetime = DateTime.Now;
        LeadCallDetailsDTO obj = new LeadCallDetailsDTO();
        int minPointDeductTime = Convert.ToInt16("minPointDeductTime".AppSettings());
        try
        {
            if (leadId <= 0 || leadId == 111)
                return obj;
            DataSet ds = LeadPrioritizationDLL.GetLeadCallDetails(leadId, true);
            if (ds != null)
            {
                if (ds.Tables != null)
                {
                    if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        long UID = 0;
                        if (!string.IsNullOrEmpty(CallDataID))
                            UID = Convert.ToInt64(CallDataID);
                        obj.TotalDuration = ds.Tables[0].AsEnumerable().Sum(x => x.Field<Int16>("Duration"));
                        obj.Totaltalktime = ds.Tables[0].AsEnumerable().Sum(x => x.Field<Int16>("talktime"));
                        obj.lastNtalktime = ds.Tables[0].AsEnumerable().OrderByDescending(m => m.Field<DateTime>("CreatedOn")).Take(1).Sum(
                             x => x.Field<Int16>("talktime"));
                        obj.TodayTalktime = ds.Tables[0].AsEnumerable().Where(m => m.Field<DateTime>("CreatedOn") > DateTime.Now.Date).Sum(
                             x => x.Field<Int16>("talktime"));
                        if (ds.Tables[0].Rows.Count > 1)
                        {
                            obj.LastCallTime = ds.Tables[0].AsEnumerable().Where(m => m.Field<long>("CallDataID") != UID).Max(m => m.Field<DateTime>("CreatedOn"));
                            obj.lastNtalktime = ds.Tables[0].AsEnumerable().OrderByDescending(m => m.Field<DateTime>("CreatedOn")).Take(2).Sum(
                            x => x.Field<Int16>("talktime"));
                        }
                        var userId = ds.Tables[0].AsEnumerable().OrderByDescending(m => m.Field<DateTime>("CreatedOn")).Select(x => Convert.ToInt64(x["UserID"])).ToList().FirstOrDefault();
                        Int64.TryParse(Convert.ToString(userId), out obj.userID);

                        string Mobile = ds.Tables[0].AsEnumerable().OrderByDescending(m => m.Field<DateTime>("CreatedOn")).Select(x => x.Field<String>("Phone")).ToList().FirstOrDefault();
                        MobileNo = Mobile;

                        obj.NANC_Attempts = Convert.ToInt16(ds.Tables[0].AsEnumerable().Where(x => x.Field<Int16>("talktime") == 0 && x.Field<Int16>("Duration") >= minPointDeductTime).Count());

                        /** stop verify mobile number process
                        

                        byte MobileStatus = LeadPrioritizationDLL.GetMobileStatusInRedis(Mobile);
                        if (MobileStatus == 2)
                        {
                            int talktime = ds.Tables[0].AsEnumerable().Where(x => x.Field<String>("Phone") == Mobile).Sum(x => x.Field<Int16>("talktime"));
                            int callCount = ds.Tables[0].AsEnumerable().Where(x => x.Field<String>("Phone") == Mobile && x.Field<Int16>("talktime") == 0).Count();

                            if (talktime > 0)
                                obj.MobileStatus = MobileStatusEnum.Valid;
                            else if (callCount >= 4)
                                obj.MobileStatus = MobileStatusEnum.InValid;
                            else
                                obj.MobileStatus = MobileStatusEnum.Default;

                            if (Convert.ToByte(obj.MobileStatus) > 0)
                                LeadPrioritizationDLL.SetVerifyMobileStatus(Mobile, obj.MobileStatus, Convert.ToString(ProductID));
                        }
                        **/

                    }
                }
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "GetLeadTalkTime", "OneLeadPriority", string.Empty, string.Empty, string.Empty, _RequestDatetime, DateTime.Now);
        }
        return obj;
    }
    public int UpdateLeadStatus(LeadCallDetailsDTO obj)
    {
        int updatedStatus = 0;
        int totalTalkTime = 0;
        int.TryParse(("TotalTalkTime" + obj.productId.ToString()).AppSettings(), out totalTalkTime);
        if (obj.Totaltalktime > 0 && totalTalkTime > 0)
        {
            int statusId = 3;
            int lastTalkTime = 0;
            int ProspectTalkTime = 0;
            int contactedTalkTime = 0;

            int.TryParse(("Contacted" + obj.productId.ToString()).AppSettings(), out contactedTalkTime);
            if (("Contacted" + obj.productId.ToString()).AppSettings() != "")
            {
                statusId = 0;
            }

            int.TryParse(("LastTalkTime" + obj.productId.ToString()).AppSettings(), out lastTalkTime);
            int.TryParse(("ProspectTalkTime" + obj.productId.ToString()).AppSettings(), out ProspectTalkTime);

            if (obj.lastNtalktime >= contactedTalkTime && contactedTalkTime > 0)
            {
                statusId = 3;
            }

            if (obj.Totaltalktime >= totalTalkTime || obj.lastNtalktime >= lastTalkTime)
            {
                statusId = 4;
            }
            if (ProspectTalkTime > 0 && obj.lastNtalktime >= ProspectTalkTime)
            {
                statusId = 11;
            }
            if (ProspectTalkTime > 0 && (obj.productId == 3 || obj.productId == 139) && obj.Totaltalktime >= ProspectTalkTime)
            {
                statusId = 11;
            }
            if (statusId > 0)
            {
                LeadPrioritizationDLL.UpdateLeadStatusByAutomation(obj.ParentId, statusId, 0, 124);
                updatedStatus = statusId;
            }
            else
            {
                if (obj.TotalDuration > 0 && obj.productId.Equals(131))
                {
                    LeadPrioritizationDLL.UpdateLeadStatusByAutomation(obj.ParentId, 2, 0, 124);
                    updatedStatus = 2;
                }
            }
        }
        else if (obj.TotalDuration > 0 && obj.productId.Equals(131))
        {
            LeadPrioritizationDLL.UpdateLeadStatusByAutomation(obj.ParentId, 2, 0, 124);
            updatedStatus = 2;
        }
        return updatedStatus;
    }

    public void PrepNDumpPriorityLogToQueue(Int64 UserID, bool FlagPositioningLog, DateTime? AppearingTime, Int16? Position, long LeadId, byte? AppearReasonId, string EmpID, byte? IsPriorityLead, DateTime? WorkDoneTs, byte? ActionId)
    {
        LeadPriorityLog oLeadPriorityLog = new LeadPriorityLog();
        DateTime RequestDatetime = DateTime.Now;
        if (RequestDatetime.Hour >= 20 && RequestDatetime.Hour < 8)
            return;
        try
        {
            if (UserID <= 0)
            {
                if (!string.IsNullOrEmpty(EmpID))
                {
                    var _masterData = MasterData.EmployeeMaster();
                    if (_masterData.ContainsKey(EmpID))
                    {
                        var userData = _masterData[EmpID];
                        UserID = Convert.ToInt64(userData.Split("-")[0]);                        
                    }
                }
            }
            oLeadPriorityLog.LeadID = LeadId;
            oLeadPriorityLog.UserID = UserID;
            oLeadPriorityLog.FlagPositioningLog = FlagPositioningLog;
            oLeadPriorityLog.AppearedReasonId = AppearReasonId;
            oLeadPriorityLog.LeadAppearedAt = AppearingTime;
            oLeadPriorityLog.Position = Position;
            oLeadPriorityLog.IsPriorityLead = IsPriorityLead;
            oLeadPriorityLog.WorkDoneTs = WorkDoneTs;
            oLeadPriorityLog.ActionId = ActionId;

            InsertPriorityLogToSql(oLeadPriorityLog);
        }
        catch (Exception ex)
        {
            string exception = ex.ToString();
            LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, exception, "DumpPriorityLogToQueue", "LeadPrioritizationBLL", "Scheduler", "", string.Empty, RequestDatetime, DateTime.Now);
        }
    }
    public void InsertPriorityLog(LeadPriorityLog oLeadPriorityLog)
    {
        StringBuilder str = new StringBuilder();
        oLeadPriorityLog.WorkDoneTs = DateTime.Now;// replacing Client side time with Application Level time   
        InsertPriorityLogToSql(oLeadPriorityLog);
    }
    public void InsertPriorityLogToSql(LeadPriorityLog oLeadPriorityLog)
    {
        bool IsLogSqlDumpOn = oLeadPriorityConstants.PriorityLogConstant.IsLogSqlDumpOn;
        if (IsLogSqlDumpOn)
            LeadPrioritizationDLL.InsertPriorityLogToSql(oLeadPriorityLog);
    }
    public Boolean AddLPDataPointInQueue(PriorityModel oPriorityModel)
    {

        try
        {
            oPriorityModel.ts = DateTime.Now;
            UpdatePriorityModelNLeadPoint(oPriorityModel);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", oPriorityModel.LeadID, ex.ToString(), "AddLPDataPointInQueue", "LeadPrioritizationBLL", "OneLeadPriority", "", string.Empty, DateTime.Now, DateTime.Now);
            return false;
        }

        return true;
    }

    private void UpdateCustomerNANC(PriorityModel reqPriorityModel, long custID=0)
    {
        try
        {
            List<string> nonNANCcalltypes = new() { "IB", "VIDEOMEET", "SCREENSHARE" };

            if (reqPriorityModel != null && reqPriorityModel.Call != null && reqPriorityModel.Call.Duration >= 2 && reqPriorityModel.Call.TalkTime == 0
                && reqPriorityModel.Call.CallType != null && !nonNANCcalltypes.Contains(reqPriorityModel.Call.CallType.ToUpper())
            )
            {
                if (custID == 0)
                {
                    // get from sql
                    DataSet ds = LeadPrioritizationDLL.GetLeadDetails_SQL(reqPriorityModel.LeadID);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["CustomerId"] != null) {
                        custID = (long)ds.Tables[0].Rows[0]["CustomerId"];
                    }
                }
                if (custID > 0)
                {
                    CustomerNANC ocustomerNANC = new()
                    {

                        CallDate = reqPriorityModel.Call.calltime,
                        CustID = custID,
                        UpdatedOn = DateTime.Now
                    };
                    LeadPrioritizationDLL.InsertUpdateCustNANCInMongo(ocustomerNANC);
                }
            }
        }
        catch(Exception ex) {
            LoggingHelper.LoggingHelper.AddloginQueue("", reqPriorityModel.LeadID, ex.ToString() , "UpdateCustomerNANC", "LeadPrioritizationBLL", "Automation","", custID.ToString(), DateTime.Now, DateTime.Now);
        }
    }

    private bool IsValidNANC(PriorityModel _PriorityModel)
    {
        string[] _dispositions = { "403", "404", "408", "500" };
        if (_PriorityModel.Call != null && _PriorityModel.Call.Duration > 2 && _PriorityModel.Call.TalkTime < 15)
        {
            if (!string.IsNullOrEmpty(_PriorityModel.Call.Disposition) && _dispositions.Contains(_PriorityModel.Call.Disposition))
            {
                return false;
            }
            if (!string.IsNullOrEmpty(_PriorityModel.Call.Disposition) && _PriorityModel.Call.Disposition == "503" && _PriorityModel.Call.Duration < 10)
            {
                return false;
            }

            return true;
        }
        else
            return false;
    }
    public AddLeadValidation ValidateAddLeadToPriorityQueue(UserNext5Leads priorityLead)
    {
        DateTime dtreq = DateTime.Now;
        PriorityModel data = new PriorityModel();
        AddLeadValidation _AddLeadValidation = new AddLeadValidation();
        _AddLeadValidation.status = 1;
        _AddLeadValidation.message = "Success";
        List<Int16> InvestmentLeadrans = new List<short>() { 1, 2, 3, 4 };
        List<Int16> HealthLeadrans = new List<short>() { 231, 232, 233, 234 };
        List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115, 117 };
        string utmSource = string.Empty;
        string leadSource = string.Empty;
        var productId = 0;
        bool status = false;
        try
        {
            if (priorityLead == null || priorityLead.Leads == null || priorityLead.Leads.Count == 0)
            {
                _AddLeadValidation = new AddLeadValidation() { LeadID = 0, status = 0, message = "Request Data is not in valid format" };
                return _AddLeadValidation;
            }
            List<Int32> ReasonIds = new List<int>() { 34, 37 };
            List<Int32> HNIGroupIDs = "HNIGroupIDs".AppSettings().Split(',').Select(Int32.Parse).ToList();
            int CallBackWaitTime = Convert.ToInt32("CallBackWaitTime".AppSettings());
            long LeadId = priorityLead.Leads[0].LeadId;
            int Unansweredallowed = 4;
            bool leadAppointmentToday = false;
            data = LeadPrioritizationDLL.GetLeadDetails(LeadId);
            if (data != null && data.ProductID == 131 && priorityLead.Leads != null && priorityLead.Leads.Count > 0)
            {
                var ds = LeadPrioritizationDLL.getParentID((priorityLead.Leads[0].LeadId));
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    utmSource = ds.Tables[0].Rows[0]["UtmSource"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["UtmSource"]) : string.Empty;
                    leadSource = ds.Tables[0].Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["LeadSource"]) : string.Empty;
                    productId = ds.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["ProductID"]) : default;

                }
            }
            if (data == null)
            {
                var ds = LeadPrioritizationDLL.getParentID((priorityLead.Leads[0].LeadId));
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["ParentID"] != null)
                {
                    long Parentid = Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]);
                    utmSource = ds.Tables[0].Rows[0]["UtmSource"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["UtmSource"]) : string.Empty;
                    leadSource = ds.Tables[0].Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["LeadSource"]) : string.Empty;
                    productId = ds.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["ProductID"]) : default;
                    if (Parentid > 0)
                        data = LeadPrioritizationDLL.GetLeadDetails(Parentid);
                }
            }
            
            if (data != null && data.LeadID > 0)
            {
                if (priorityLead.Source == "fosapp" && data.Appointment != null && data.Appointment.ScheduledOn.Date == DateTime.Now.Date)
                {
                    Unansweredallowed = 6;
                    leadAppointmentToday = true;
                }
                else
                    Unansweredallowed = 4;
                status = LeadPrioritizationDLL.IsCorrectTimeToCall(data.Country);

                if (data.DNC != null && data.DNC.ts != DateTime.MinValue && 
                    DateTime.Now.Subtract(data.DNC.ts).Days <= data.DNC.CoolingPeriod && 
                    data.DNC.CoolingPeriod != 30 && 
                    !(leadAppointmentToday && data.DNC.CoolingPeriod == 1) && 
                    !(data.DNC.LastIbCallTime != DateTime.MinValue && 
                      data.DNC.LastIbCallTime <= DateTime.Now && 
                      DateTime.Now.Subtract(data.DNC.LastIbCallTime).TotalHours <= 24)) // Also do not restrict if there was an inbound call within last 24 hours
                {
                    // DNC : do not restrict if today's appointment is present on lead, for dnc=1 cases(orange)
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID is marked as Do Not Call. Please do not call the customer" };
                }
                else if (!ReasonIds.Contains(priorityLead.Leads[0].ReasonId) && data.Call != null && data.CustomerCalltime.Date == DateTime.Now.Date && data.CustomerTodayNANC >= 6)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID has crossed 6 unanswered calls limit for customer. Please try again tomorrow." };
                    // _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has exceed to max(6) Not answered attempts for customer" };
                }
                else if (!ReasonIds.Contains(priorityLead.Leads[0].ReasonId) && data.Call != null && data.Call.calltime.Date == DateTime.Now.Date && data.Call.TodaysNANCAttempt >= Unansweredallowed)
                {
                    // _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has exceed to max Not answered attempts." };
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID has crossed "+ Unansweredallowed + " unanswered calls limit. Please try again tomorrow." };

                }
                else if (ReasonIds.Contains(priorityLead.Leads[0].ReasonId) && data.Call != null && data.CustomerCalltime.Date == DateTime.Now.Date && data.CustomerNANCAttempt >= Unansweredallowed)
                {
                    // _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has exceed to max Not answered attempts for customer" };
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID has crossed " + Unansweredallowed + " unanswered calls limit for customer. Please try again tomorrow." };

                }
                else if (ReasonIds.Contains(priorityLead.Leads[0].ReasonId) && !status && data.ProductID != 117)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has incompatible time zone time." };
                }
                //else if (priorityLead.Source != "fosapp" && (data.LeadSource == null || data.LeadSource.ToUpper() != "RENEWAL") && ExcessiveProducts.Contains(data.ProductID) && data.User.FirstAssignedOn.Date < DateTime.Now.Date && data.Call != null && data.Call.calltime.Date == DateTime.Now.Date && data.Call.TotalTT < 60)
                //{
                //    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has total talktim < 1 min, manual attempts not allowed" };
                //}
                /*
                else if (data.ProductID == 7 && data.LeadID % 2 == 0 && data.Income >= 1500000 && data.User.FirstAssignedOn.Date < DateTime.Now.Date && data.Call != null && data.Call.TotalTT < 60)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has talktim < 1 min." };
                }
                else if (data.ProductID == 115 && InvestmentLeadrans.Contains(data.LeadRank) && data.LeadID%2==0 && data.User.FirstAssignedOn.Date < DateTime.Now.Date && data.Call!=null && data.Call.TotalTT < 60)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has talktim < 1 min." };
                }
                else if (data.ProductID == 2 && HealthLeadrans.Contains(data.LeadRank) && data.LeadID % 2 == 0 && data.User.FirstAssignedOn.Date < DateTime.Now.Date && data.Call != null && data.Call.TotalTT < 60)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Sorry lead id " + data.LeadID + " has talktim < 1 min." };
                }*/
                //Agents cannot call before 15 minutes of customer req call back time (for HNI Cusromers only)
                else if (priorityLead.Leads[0].IsNeedToValidate == 1
                    && data.CallBack != null && data.CallBack.CallBackType == CallBackTypeEnum.CustRequested
                    && data.CallBack.CBtime > DateTime.Now.AddMinutes(CallBackWaitTime)
                    && (data.Call == null || !(data.Call.calltime > DateTime.Now.AddMinutes(-15) && data.Call.TodayTalktime >= 10)))
                //&& (!(data.Call != null && data.Call.calltime.AddMinutes(30) >= DateTime.Now && data.Call.TalkTime >= 10)))
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Customer Scheduled Call back - Sorry LeadId" + data.LeadID + " cannot be added to calling queue before scheduled date and time " };
                }
                else if (data.ProductID > 0 && data.ProductID == 131 && !string.IsNullOrEmpty(utmSource) && utmSource.Equals("Medicalassociation", StringComparison.OrdinalIgnoreCase) &&
                        !string.IsNullOrEmpty(leadSource) && leadSource.Equals("ExternalSources", StringComparison.OrdinalIgnoreCase))
                {
                        _AddLeadValidation = new AddLeadValidation()
                        {
                            LeadID = data.LeadID,
                            status = 0,
                            message = "Oops! This lead cannot be added to the queue due to restricted utmsource and leadsource values."
                        };
                }
                else if (priorityLead.Leads[0].IsNeedToValidate == 1)
                {
                    Result callAllowed = isAttemptsExceeded(data);
                    _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = (callAllowed.status == true ? 1 : 0), message = (callAllowed.status == true) ? "Success" : callAllowed.message };
                }

                if (_AddLeadValidation.status == 1 && _AddLeadValidation.message == "Success")
                {
                    // check NANC attempts (Mongo-CustomerNanc)
                    if (data.Call != null && data.Call.calltime.Date == DateTime.Now.Date && GetCustNANCAttemptsMongo(LeadId, data.CustID) >= 6)
                    {
                        _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID has crossed 6 unanswered calls limit for customer. Please try again tomorrow." };
                        return _AddLeadValidation;
                    }

                    // check NANC attempts (SQL)
                    if (data.Call != null && data.Call.calltime.Date == DateTime.Now.Date && LeadPrioritizationDLL.GetNANCAttempts(data.LeadID) >= 6)
                    {
                        _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Oops! LeadID has crossed 6 unanswered calls limit for customer. Please try again tomorrow!" };
                        return _AddLeadValidation;
                    }
                }

                if (_AddLeadValidation.status == 1 && _AddLeadValidation.message == "Success" && priorityLead.Leads[0].IsAddLeadtoQueue == 1)
                {
                    bool isLeadAddedtoPQueue = AddLeadToPriorityQueue(priorityLead);
                    if (!isLeadAddedtoPQueue)
                    {
                        _AddLeadValidation = new AddLeadValidation() { LeadID = data.LeadID, status = 0, message = "Error adding LeadId " + data.LeadID + " to queue." };
                    }
                }
            }
            else if (priorityLead.Leads[0].IsAddLeadtoQueue == 1 && priorityLead.Leads[0].IsNeedToValidate != 1)
            {

                // check NANC attempts (Mongo-CustomerNanc)
                if (GetCustNANCAttemptsMongo(LeadId) >= 6)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = LeadId, status = 0, message = "Oops! LeadID has crossed 6 unanswered calls limit for customer. Please try again tomorrow." };
                    return _AddLeadValidation;
                }
                if (LeadPrioritizationDLL.GetNANCAttempts(LeadId) >= 6)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = LeadId, status = 0, message = "Oops! LeadID has crossed 6 unanswered calls limit for customer. Please try again tomorrow!" };
                    return _AddLeadValidation;
                }
                bool isLeadAddedtoPQueue = AddLeadToPriorityQueue(priorityLead);
                if (!isLeadAddedtoPQueue)
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = priorityLead.Leads[0].LeadId, status = 0, message = "Error adding LeadId " + priorityLead.Leads[0].LeadId + " to queue." };
                }
                else
                {
                    _AddLeadValidation = new AddLeadValidation() { LeadID = priorityLead.Leads[0].LeadId, status = 1, message = "Success" };
                }
            }
            else if (productId > 0 && productId == 131 && !string.IsNullOrEmpty(utmSource) && utmSource.Equals("Medicalassociation", StringComparison.OrdinalIgnoreCase) &&
                        !string.IsNullOrEmpty(leadSource) && leadSource.Equals("ExternalSources", StringComparison.OrdinalIgnoreCase))
            {
                _AddLeadValidation = new AddLeadValidation()
                {
                    LeadID = LeadId,
                    status = 0,
                    message = "Oops! This lead cannot be added to the queue due to restricted utmsource and leadsource values."
                };
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(priorityLead.Leads[0].LeadId),
                priorityLead.Leads[0].LeadId, ex.ToString(),
                "ValidateAddLeadToPriorityQueue", "OneLeadPriority", "LeadPrioritizationController",
                JsonConvert.SerializeObject(priorityLead), JsonConvert.SerializeObject(_AddLeadValidation),
                dtreq, DateTime.Now);
        }
        return _AddLeadValidation;
    }

    private short GetCustNANCAttemptsMongo(long leadId, long custID=0)
    {
        try
        {
            if (custID == 0)
            {
                // get from sql
                DataSet ds = LeadPrioritizationDLL.GetLeadDetails_SQL(leadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["CustomerId"] != null)
                {
                    custID = (long)ds.Tables[0].Rows[0]["CustomerId"];
                }
            }
            if (custID == 0)
            {
                // Cases where customerid can not be fetched : TO DISCUSS
                return 0;
                //throw new Exception("Unable to get CustomerId");
            }
            return LeadPrioritizationDLL.GetCustNANCAttemptsMongo(custID);

        }
        catch (Exception ex) {
            LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "GetCustNANCAttemptsMongo", "LeadPrioritizationBLL", "Automation", "", custID.ToString(), DateTime.Now, DateTime.Now);
            return 10;
        }
    }

    public bool AddLeadToPriorityQueue(UserNext5Leads priorityLead)
    {
        bool isCallable = true;
        bool result = false;

        try
        {
            if (priorityLead.Leads[0].LeadId > 0)
            {
                long incomingleadId = priorityLead.Leads[0].LeadId;
                long parentId = priorityLead.Leads[0].LeadId;
                var ds = LeadPrioritizationDLL.getParentID((priorityLead.Leads[0].LeadId));
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["ParentID"] != null)
                {
                    parentId = Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]);
                    var name = Convert.ToString(ds.Tables[0].Rows[0]["Name"]);
                    priorityLead.Leads[0].LeadId = parentId;
                    priorityLead.Leads[0].Name = name;
                    priorityLead.Leads[0].ProductId = Convert.ToByte(ds.Tables[0].Rows[0]["ProductId"]);
                    if (priorityLead.Leads[0].CustomerId == 0)
                    {
                        priorityLead.Leads[0].CustomerId = Convert.ToInt64(ds.Tables[0].Rows[0]["CustomerID"]);
                    }
                }

                if (priorityLead.Leads[0].ReasonId == 30 && priorityLead.Leads[0].ProductId == 115)
                {
                    isCallable = LeadPrioritizationDLL.IsLeadCallableByPolicyStatus(incomingleadId);
                }

                if (isCallable)
                {
                    if (priorityLead.Leads[0].ReasonId == 35) // not external IP
                    {
                        LeadPrioritizationBLL leadPrioritizationBll = new LeadPrioritizationBLL();
                        PriorityModel priorityModel = new PriorityModel
                        {
                            LeadID = parentId,
                            ActiveLeadSet = new List<long> { priorityLead.Leads[0].LeadId },
                            Revisit = new RevisitData { ts = DateTime.Now, RevisitType = RevisitType.Ctc },
                            EventType = EventTypeEnum.Revisit,
                            LeadStatus = new LeadStatusData { StatusID = 1 },
                            PageName = "CTC click"
                        };
                        leadPrioritizationBll.AddLPDataPointInQueue(priorityModel);
                    }

                    LeadPrioritizationDLL.UpdateWorkDoneLog(priorityLead.Leads[0].LeadId, Convert.ToInt32(priorityLead.UserId), priorityLead.Leads[0].ReasonId, string.Empty, 0, 1);
                    result = LeadPrioritizationDLL.AddLeadToPriorityQueue(priorityLead);
                }
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", priorityLead.Leads[0].LeadId, ex.ToString(), "AddLeadToPriorityQueue", "OneLead", "LeadPrioritizationBLL", JsonConvert.SerializeObject(priorityLead), string.Empty, DateTime.Now, DateTime.Now);
        }
        return result;
    }

    public Result isAttemptsExceeded(PriorityModel _priorityModel)
    {
        Result _Response = new Result();
        _Response.status = true;
        bool isRenewalGroup = false;
        Int64 _UserId = 0;
        Int16 _GroupID = 0;
        Int16 _processId = 1;
        List<Int16> healthProducts = new List<short>() { 2, 106, 118, 130 };
        List<Int16> churnProducts = "ChurnProducts".AppSettings().Split(',').Select(Int16.Parse).ToList();
        List<Int16> RestrictCalling = "RestrictCallingPrds".AppSettings().Split(',').Select(Int16.Parse).ToList();

        string CurrentCallShift = CalculateCallShift(DateTime.Now);
        DateTime CurrentTime = DateTime.Now;
        LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();

        //var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, MongoCollection.LPDataCollection(), fields);
        if (_priorityModel == null)
            return _Response;

        if (_priorityModel.User != null)
            _UserId = _priorityModel.User.UserID;

        OneLeadObj oneLeadObj = LeadPrioritizationDLL.GetOneLeadObj(_UserId);
        if (oneLeadObj != null)
        {
            _processId = oneLeadObj.ProcessId;
            _GroupID = oneLeadObj.GroupId;
        }
        else
        {
            var ds = LeadPrioritizationDLL.GetGroupNameandID(_UserId);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                _processId = (ds.Tables[0].Rows[0]["ProcessID"] == null || ds.Tables[0].Rows[0]["ProcessID"] == DBNull.Value) ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["ProcessID"]);
                _GroupID = Convert.ToInt16(ds.Tables[0].Rows[0]["UserGroupID"]);
            }
        }

        if (_processId == 4)
            isRenewalGroup = true;

        var _ProductLeadPriorityConstants = LeadPrioritizationDLL.GetPriorityConfigByProduct(_priorityModel.ProductID, isRenewalGroup, _GroupID);
        if (_ProductLeadPriorityConstants == null)
            return _Response;

        List<DateTime> todayExpCall = _ProductLeadPriorityConstants.TodayExpCall;
        if (todayExpCall.Contains(DateTime.Now.Date) && (_priorityModel.CallReleaseTime.Date != DateTime.Now.Date
            || _priorityModel.Call == null || _priorityModel.Call.calltime.Date != DateTime.Now.Date))
        {
            _Response.message = "Sorry you are not allowed to call on this lead today.";
            _Response.status = false;
        }


        if (_priorityModel.Call == null)
            return _Response;


        short Week1MaxAttempts = _ProductLeadPriorityConstants.Week1MaxAttempts;
        short WeekMaxAttempts = _ProductLeadPriorityConstants.WeekMaxAttempts;

        if (!(((Convert.ToInt32(Convert.ToInt32(CurrentTime.Date.Subtract(Convert.ToDateTime(_priorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1) != _priorityModel.Call.Current_Week) || (_priorityModel.Call.Current_Week == 1 ? _priorityModel.Call.Week_Attempt < Week1MaxAttempts : (_priorityModel.Call.Week_Attempt < WeekMaxAttempts)))) //week attempts
        {
            _Response.message = "Sorry your Weekly attempts are exausted.";
            _Response.status = false;
        }
        else if (_priorityModel.Call != null && _priorityModel.DNC != null && 
                 (CurrentTime.Date.Subtract(_priorityModel.DNC.ts.Date).TotalDays < _priorityModel.DNC.CoolingPeriod) && 
                 !(_priorityModel.DNC.LastIbCallTime != DateTime.MinValue && DateTime.Now.Subtract(_priorityModel.DNC.LastIbCallTime).TotalHours <= 24))
        {
            // Allow calling if there was an inbound call within last 24 hours
            _Response.message = "Sorry this lead is marked as do not call category.";
            _Response.status = false;
        }
        else
        {
            var Callrestrction = LeadPrioritizationDLL.GetRestrictLeadAttempts(_priorityModel.LeadID);
            if (Callrestrction == 1)
            {
                _Response.status = false;
                if (_priorityModel.Call != null)
                {
                    Int16 waitTime = 120;
                    try {
                        waitTime = Convert.ToInt16(120 - DateTime.Now.Subtract(_priorityModel.Call.calltime).TotalMinutes);
                    }
                    catch { }
                    if (waitTime <= 0)
                    {
                        waitTime = 120;
                    }
                    // _Response.message = "Sorry you can add this lead after " + waitTime + " mins";
                    _Response.message = "Oops! The customer-level limit of 2 unanswered attempts within 30 minutes has been reached. Please try again after " + waitTime + " mins.";
                    // _Response.message = "Oops! LeadID has crossed 2 unanswered attempt limit in 30 mins. Please try again in "+waitTime+" mins.";

                }
                else
                    _Response.message = "Oops! The customer-level limit of 2 unanswered attempts within 30 minutes has been reached. Please try again after 2 hours.";
            }
            else if (Callrestrction == 2)
            {
                _Response.status = false;
                // _Response.message = "You have reached the limit of 4 manual attempts. No more manual attempts are allowed today";
                _Response.message = "Oops! LeadID has crossed  4 manual attempts limit. Please try again tomorrow.";
            }
            else if (Callrestrction == 3)
            {
                _Response.status = false;
                _Response.message = "Oops! As Talktime for today is less than 3 min, only 2 manual attempts are allowed";
            }

        }

        return _Response;
    }

    //This method will return callShift based on call time(Morning,Afternoon,Evening)
    public static string CalculateCallShift(DateTime callDate)
    {
        string callShift = "X";// default value
        try
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            byte callingHr = 0;
            if (callDate != null)
            {
                callingHr = Convert.ToByte(callDate.Hour);
                if (callingHr >= _LeadPriorityConstants.MorningShift && callingHr < _LeadPriorityConstants.AfterNoonShift)
                    return "M";
                else if (callingHr >= _LeadPriorityConstants.AfterNoonShift && callingHr < _LeadPriorityConstants.EveningShift)
                    return "A";
                else if (callingHr >= _LeadPriorityConstants.EveningShift)
                    return "E";
            }
        }
        catch (Exception ex)
        {

        }
        return callShift;
    }

    public int GetReleaseLeadsCount(Int64 UserID)
    {
        try
        {
            return LeadPrioritizationDLL.GetReleaseLeadsCount(UserID);
        }
        catch (Exception ex)
        {
            return 0;
        }
    }

    public bool INSERTLeadReopenTrack(long LeadId, int SubStatusID, Int64 UserID)
    {
        return LeadPrioritizationDLL.INSERTLeadReopenTrack(LeadId, SubStatusID, UserID);
    }

    public void ReleaseRequest(ReleaseLeadRequest _ReleaseLeadRequest)
    {
        string exception = string.Empty;
        StringBuilder sb = new StringBuilder();
        DateTime RequestDatetime = DateTime.Now;
        try
        {
            sb.Append(_ReleaseLeadRequest.ToString());
            if (_ReleaseLeadRequest != null && _ReleaseLeadRequest.LeadIds != null)
            {
                foreach (var LeadID in _ReleaseLeadRequest.LeadIds)
                {
                    PriorityModel oPriorityModel = new PriorityModel() { LeadID = LeadID, EventType = EventTypeEnum.ReleaseLeads, ReleaseStatus = _ReleaseLeadRequest.Status };
                    LeadPrioritizationDLL.ReleaseRequest(_ReleaseLeadRequest.UserId, LeadID, _ReleaseLeadRequest.Status);
                    if (_ReleaseLeadRequest.Status == 3)// Request Declined(Rejected)
                    {
                        List<long> leads = LeadPrioritizationDLL.GetActiveSetMongo(LeadID);
                        if (leads != null && leads.Count > 0)
                        {
                            LeadPrioritizationDLL.RejectMatrixLeads(String.Join(",", leads), _ReleaseLeadRequest.UserId);
                        }
                    }
                    else if (_ReleaseLeadRequest.Status == 2)// Request Accepted
                    {
                        //oPriorityModel = new PriorityModel() { LeadID = LeadID, EventType = EventTypeEnum.ReleaseLeads, ReleaseStatus = _ReleaseLeadRequest.Status };
                        UpdateReleaseLog(LeadID, _ReleaseLeadRequest.UserId);
                    }

                    AddLPDataPointInQueue(oPriorityModel);
                }
            }
        }
        catch (Exception ex)
        {
            exception = ex.ToString();
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", 0, exception, "ReleaseRequest", "LeadPrioritizationBLL", "Communication", sb.ToString(), string.Empty, RequestDatetime, DateTime.Now);
        }
    }
    public static void UpdateReleaseLog(Int64 LeadId, Int64 UserID)
    {
        try
        {
            string comments = string.Empty;
            string employee = "System-User";

            LeadPrioritizationDLL.UpdateCallReleaseRequestHistory(Convert.ToInt64(LeadId), UserID);
            var AgentData = LeadPrioritizationDLL.getAgentData(UserID);
            if (AgentData != null && AgentData.UserName != null)
                employee = UserID == 124 ? "System" : AgentData.UserName + " (" + AgentData.EmployeeId + ") ";

            comments = "Call Released by " + employee;
            LeadPrioritizationDLL.LogLeadHistory(UserID, Convert.ToInt64(LeadId), comments);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadId), LeadId, ex.ToString(), "UpdateReleaseLog", "LeadPrioritizationBLL", "Communication", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
        }
    }
    public UnAnsweredSummary GetUnAnsweredSummary(string LeadID, string ProductID)
    {
        if (string.IsNullOrEmpty(LeadID) || LeadID == "0")
            return new UnAnsweredSummary();
        if (string.IsNullOrEmpty(ProductID))
            ProductID = "117";
        return LeadPrioritizationDLL.GetUnAnsweredSummary(Convert.ToInt64(LeadID), Convert.ToInt16(ProductID));
    }
    public ResponseAPI IsCallBackAllowed(string LeadId, string CBTime)
    {

        ResponseAPI _Response = new ResponseAPI();
        _Response.status = true;
        try
        {
            //if (oLeadPriorityConstants.IsCBRestrictionON == false)
            //    return _Response;
            //else
            return LeadPrioritizationDLL.IsCallBackAllowed(Convert.ToInt64(LeadId), CBTime);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(LeadId, Convert.ToInt64(LeadId), ex.ToString(), "IsCallBackAllowed", "LeadPrioritizationBLL", "", "", "", DateTime.Now, DateTime.Now);
            return _Response;
        }
    }
    public static Tuple<string, string> GetCountryPermissibleCallTime(int CountryId)
    {
        string PermissibleTime = string.Empty;
        string Country = string.Empty;
        try
        {
            if (CountryId > 0 && CountryId != 392)
            {
                List<CountryTimeZone> lstTimeZones = MasterData.GetCountryTimeZone();
                List<CountryTimeZone> sortedlstTimeZones = lstTimeZones.Where(r => r.CountryId == CountryId).ToList();
                var StartTime = sortedlstTimeZones != null && sortedlstTimeZones.Count > 0 ? sortedlstTimeZones[0].StartTime.ToString() : "";
                var EndTime = sortedlstTimeZones != null && sortedlstTimeZones.Count > 0 ? sortedlstTimeZones[0].EndTime.ToString() : "";
                Country = sortedlstTimeZones != null && sortedlstTimeZones.Count > 0 ? sortedlstTimeZones[0].Country.ToString() : "";
                if (!string.IsNullOrEmpty(StartTime) && !string.IsNullOrEmpty(EndTime))
                {
                    PermissibleTime = ConvertTimetoMeridiemFormat(StartTime.ToString()) + " - " + ConvertTimetoMeridiemFormat(EndTime.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(CountryId.ToString(), CountryId, ex.ToString(), "GetCountryPermissibleCallTime", "LeadPrioritizationBLL", "Communication", "0", ex.ToString(), DateTime.Now, DateTime.Now);
        }
        return new Tuple<string, string>(PermissibleTime, Country);
    }
    public static string ConvertTimetoMeridiemFormat(string InputTime)
    {
        string OutputTime = "";
        try
        {
            string[] splitTime = InputTime.Split(':');
            if (splitTime.Length > 1)
            {
                int hours = Convert.ToInt32(splitTime[0]);
                string Meridian = hours <= 11 ? "AM" : "PM";
                hours = hours > 12 ? hours - 12 : hours;
                string minutes = splitTime[1].ToString();
                OutputTime = hours + ":" + minutes + Meridian;
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "ConvertTimetoMeridiemFormat", "LeadPrioritizationBLL", "Communication", InputTime, ex.ToString(), DateTime.Now, DateTime.Now);
        }
        return OutputTime;
    }
    public List<NotContactedLeads> GetNotContactedLeads(string UserId, string LastDays)
    {

        List<NotContactedLeads> list = new List<PropertyLayers.NotContactedLeads>();
        DataSet ds = LeadPrioritizationDLL.GetNotContactedLeads(Convert.ToInt64(UserId), Convert.ToInt16(LastDays));
        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
        {
            foreach (DataRow item in ds.Tables[0].Rows)
            {
                var res = GetCountryPermissibleCallTime(item["CountryId"] == DBNull.Value ? 0 : Convert.ToInt32(item["CountryId"]));
                list.Add(new PropertyLayers.NotContactedLeads()
                {
                    LeadId = Convert.ToInt64(item["LeadID"]),
                    CustName = Convert.ToString(item["CustName"]),
                    UserId = Convert.ToInt32(item["AssignedToUserID"]),
                    CreatedOn = Convert.ToDateTime(item["CreatedOn"]),
                    EventDate = item["EventDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(item["EventDate"]),
                    CallbackType = (short)(item["CallbackType"] == DBNull.Value ? 0 : Convert.ToInt16(item["CallbackType"])),
                    CustID = Convert.ToInt64(item["CustomerID"]),
                    StatusName = item["StatusName"] == DBNull.Value ? default : Convert.ToString(item["StatusName"]),
                    CallTiming = Convert.ToString(res.Item1),
                    Country = Convert.ToString(res.Item2),
                    TotalTT = Convert.ToInt32(item["TotalTT"]),
                    TotalAttempts = Convert.ToInt32(item["TotalAttempts"])

                });
            }
        }

        return list;
    }

    public bool SkipCustomer(SkipCustomerRequest skipReqObj)
    {
        DateTime RequestTime = DateTime.Now;
        string error = string.Empty;
        string comments = string.Empty;
        try
        {
            if (skipReqObj.CustID == 0)
            {
                var leadDetails = LeadPrioritizationDLL.GetLeadDataFromMongo(skipReqObj.ExcludeLeadId);
                if (leadDetails != null && leadDetails.CustID > 0)
                {
                    skipReqObj.CustID = leadDetails.CustID;
                }
                else
                {
                    comments = "Lead not found in LPdata";
                    return false;
                }
            }

            var SkipReason = skipReqObj.SkipReason;
            Int16 SkipDuration = -1;

            #region Calculate Skip Duration
            if (!string.IsNullOrEmpty(SkipReason))
            {
                var ReasonDurationDict = SkipMaster.ReasonDurationDict;
                SkipReason = SkipReason.ToUpper();

                if (ReasonDurationDict.ContainsKey(SkipReason))
                {
                    SkipDuration = ReasonDurationDict.GetValueOrDefault(SkipReason);
                }
            }
            else
            {
                if (skipReqObj.SkipDuration != null)
                {
                    SkipDuration = (short)skipReqObj.SkipDuration;
                }
                if (skipReqObj.IsMins == false)
                    SkipDuration = Convert.ToInt16(SkipDuration * 60);
            }
            #endregion

            comments = "All leads for customer Skipped For " + SkipDuration + " mins";
            if (!string.IsNullOrEmpty(SkipReason))
            {
                comments += "; reason: " + skipReqObj.SkipReason;
            }
            if (skipReqObj.ExcludeLeadId > 0)
            {
                comments += "; except: " + skipReqObj.ExcludeLeadId.ToString();
            }
            if (SkipDuration <= 0)
            {
                return false;
            }

            LeadPrioritizationDLL.SkipCustAllLead(skipReqObj.CustID, skipReqObj.ExcludeLeadId, SkipDuration);

            return true;
        }
        catch (Exception ex)
        {
            error = ex.ToString();
            return false;
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", skipReqObj.CustID, error, "SkipCustomer", "LeadPrioritizationBLL", "Communication", JsonConvert.SerializeObject(skipReqObj), comments, RequestTime, DateTime.Now);
        }


    }

    public bool PushDncData(PriorityModel oPriorityModel)
    {
        string exception = string.Empty;
        string Response = string.Empty;
        UnsubscribeLevel unsubscribeLevel = UnsubscribeLevel.DoNotUnsubscribe;
        try
        {
            PriorityModel leadData = GetPriorityModelMongo(oPriorityModel.LeadID);
            if (leadData.IsBooked == true)
            {
                Response = "Booked Lead";
                return true;
            }
            if (leadData != null)
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                UpdateBuilder<PriorityModel> update = null;
                var query = Query<PriorityModel>.EQ(p => p.LeadID, oPriorityModel.LeadID);
                long UserId = leadData.User != null ? leadData.User.UserID : 0;
                bool rejectLead = false;
                string rejectionReason = string.Empty;
                short rejectionSubStatus = 0;
                List<short> renewalCoolingOffProd = new() { 2, 117 };
                List<short> OrangeRedBlockingProds = new() { 2, 7, 115, 101, 114, 131, 3, 117 };
                bool IsRenewal = false;

                if ((leadData.LeadSource != null && leadData.LeadSource.ToUpper() == "RENEWAL") || ChkRenewalUser(UserId))
                    IsRenewal = true;


                // renewal leads case
                if (renewalCoolingOffProd.Contains(leadData.ProductID) && IsRenewal)                
                {

                    if (leadData.Call != null && leadData.Call.TotalTT > 1200)
                    {
                        Response = "Renewal lead > 20 mins";
                        return true;
                    }
                    DateTime ct = DateTime.Now;
                    var coolingPeriodMaster = LeadPrioritizationDLL.GetSentimentCoolingPeriodMaster();
                    short DncCount = (short)(oPriorityModel.DNC.CoolingPeriod / 3);

                    if (DncCount > 5) DncCount = 5;

                    var result = coolingPeriodMaster != null ? coolingPeriodMaster.Where(coolingPeriodLogic =>
                                                   leadData.PrevPolicyExpDate > DateTime.MinValue
                                                && leadData.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays >= coolingPeriodLogic.MinExpDays && leadData.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays <= coolingPeriodLogic.MaxExpDays
                                                && coolingPeriodLogic.DNCCount == DncCount
                                            ).Take(1).ToList() : null;

                    if (result != null && result.Count > 0)
                    {
                        var coolingOff = result[0].CoolingPeriod;

                        if (coolingOff > 0)
                        {
                            oPriorityModel.DNC.CoolingPeriod = coolingOff;
                        }
                        else
                        {
                            switch (coolingOff)
                            {
                                case -1: //cooloff till expiry
                                    short daysTillExpiry = (short)leadData.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays;

                                    if (daysTillExpiry == 0)
                                    {
                                        oPriorityModel.DNC.CoolingPeriod = 0; // do not call on sameday, if DNC received on expiry date - Vaibhav Saini
                                    }
                                    if (daysTillExpiry > 0)
                                    {
                                        oPriorityModel.DNC.CoolingPeriod = (short)(daysTillExpiry - 1); // call on expiry date
                                    }
                                    break;
                                case -2:
                                    unsubscribeLevel = UnsubscribeLevel.DoNotUnsubscribe;  // DO NOT unsubscribe renewal cases
                                    rejectLead = true;
                                    rejectionReason = "Do_Not_Call_AI";
                                    rejectionSubStatus = 2402;
                                    break;
                            }
                        }
                    }
                    else
                    {

                        try
                        {
                            exception = $"{oPriorityModel.LeadID}: No Matching criteria, setting coolingperiod to 0, dncCount, PrevPolicyExpDate, inputCoolingPeriod : {DncCount}, {leadData.PrevPolicyExpDate}, {oPriorityModel.DNC.CoolingPeriod}";
                        }
                        catch { }
                        oPriorityModel.DNC.CoolingPeriod = 0;
                    }
                }
                else if (
                           OrangeRedBlockingProds.Contains(leadData.ProductID)
                        && IsRenewal==false                    
                        && leadData.User != null && leadData.User.FirstAssignedOn.Date < DateTime.Now.Date.AddDays(-1) // Lead created for more than 2 days from the First time Orange & Red Sentiment. 
                        && (leadData.Call == null || leadData.Call.TotalTT < 300)                    
                        && oPriorityModel.DNC.CoolingPeriod >= 1
                )
                {
                    // BLOCK Orange and red cases 
                    oPriorityModel.DNC.CoolingPeriod = 30;
                    unsubscribeLevel = UnsubscribeLevel.UnsubCustAllLeadsWithLowTT;
                    rejectLead = false;
                    rejectionReason = "OrangeRed_CustBlock_AI";
                }
                else if (
                        OrangeRedBlockingProds.Contains(leadData.ProductID)
                        && IsRenewal == false                        
                )
                {
                    int dncCount = LeadPrioritizationDLL.getDNCCount(oPriorityModel.LeadID);
                    dncCount = dncCount + 1;

                    if (dncCount == 1)
                        oPriorityModel.DNC.CoolingPeriod = 3;
                    else if (dncCount == 2)
                        oPriorityModel.DNC.CoolingPeriod = 6;
                    else
                        oPriorityModel.DNC.CoolingPeriod = 30;

                    bool rejectCrosssell = LeadPrioritizationDLL.ChkSystemCrossSell(oPriorityModel.LeadID);
                    if (rejectCrosssell)
                    {
                        unsubscribeLevel = UnsubscribeLevel.UnsubCurrentLead;
                        rejectLead = true;
                        rejectionReason = "Negative_Sentiment_AI";
                        rejectionSubStatus = 2403;
                    }                    
                    else if (oPriorityModel.DNC.CoolingPeriod == 30)
                    {                        
                        unsubscribeLevel = UnsubscribeLevel.UnsubCustAllLeadsWithLowTT;  // Red cases, Unsubscribe at customer level
                        rejectLead = true;
                        rejectionReason = "Do_Not_Call_AI";
                        rejectionSubStatus = 2402;
                    }
                }                
                

                if (rejectLead && leadData.ProductID != 106)
                {
                    LeadPrioritizationDLL.RejectLeadOnDemand(oPriorityModel.LeadID, rejectionReason, rejectionSubStatus);
                }
                if (unsubscribeLevel != UnsubscribeLevel.DoNotUnsubscribe && leadData.ProductID != 106)
                {
                    UnsubscribeCustomer(oPriorityModel.LeadID, leadData.CustID, leadData.ProductID, leadData.Country, 30, unsubscribeLevel, rejectionReason);
                }

                if (leadData.ProductID == 106)
                {
                    // Monthly mode leads to have max cooling of 1 day - Amit Chouhan
                    oPriorityModel.DNC.CoolingPeriod = 1;
                }
                update = Update<PriorityModel>.Set(x => x.DNC.CoolingPeriod, oPriorityModel.DNC.CoolingPeriod)
                                            .Set(x => x.DNC.Score, oPriorityModel.DNC.Score)
                                            .Set(x => x.DNC.ts, DateTime.Now);

                _CommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());

                if (leadData.ProductID == 131)
                    LeadPrioritizationDLL.InsertLeadLog(oPriorityModel);
            }
        }
        catch (Exception ex)
        {
            exception = ex.ToString();
            return false;
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", oPriorityModel.LeadID, exception, "PushDncData", "LeadPrioritizationBLL", "Communication", JsonConvert.SerializeObject(oPriorityModel), Response, DateTime.Now, DateTime.Now);
        }

        return true;
    }
    public static string UnsubscribeCustomer(long leadID, long custID, short productID, short countryCode, short coolingPeriod, UnsubscribeLevel unsubscribeLevel, string SubSource = "Onelead")
    {
        dynamic dataToPost = new ExpandoObject();
        string response = "API_NOT_CALLED: lead/cust not Unsubsubscribed";
        string error = "";
        DateTime RequestTime = DateTime.Now;
        try
        {

            List<short> products = new() { productID };

            switch (unsubscribeLevel)
            {
                case UnsubscribeLevel.DoNotUnsubscribe:
                    return "";
                case UnsubscribeLevel.UnsubCurrentLead:
                    products = new() { productID };
                    break;
                case UnsubscribeLevel.UnsubCustAllLeadsWithLowTT:
                    products = new() { 2, 3, 114, 115, 7, 117, 130, 131, 101 };

                    // get all products where active leads is present with TT > 180sec
                    var ds = LeadPrioritizationDLL.GetCustInterestedProducts(custID);

                    // Do not unsubscribe leads with TT > 180sec
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            short prodId = row["productid"] != DBNull.Value ? Convert.ToInt16(row["productid"]) : (short)0;
                            if (prodId > 0 && prodId != productID)
                            {
                                products.Remove(prodId);
                            }
                        }
                    }
                    break;
                default:
                    return "";
            }


            List<dynamic> CommPreferences = new();
            Dictionary<string, string> ObjHeaders = new Dictionary<string, string>();

            string? MobileNo = "0";
            string? EncrytPhoneNo = string.Empty;
            DataTable dt = CallCommunicationDLL.GetLeadBasicDetails(leadID);
            if (dt != null && dt.Rows.Count > 0)
            {
                MobileNo = Convert.ToString(dt.Rows[0]["MobileNo"]);
            }
            if (!CoreCommonMethods.IsValidString(MobileNo) && MobileNo != "0")
            {
                throw new Exception("Mobile Number not found");
            }
            else
            {
                EncrytPhoneNo = CryptoHelper.AESencrypt_Communication(MobileNo);
            }


            dataToPost.CustomerId = custID;
            dataToPost.MobileNo = EncrytPhoneNo;
            dataToPost.CountryId = countryCode;
            dataToPost.SubSource = SubSource + "-MATRIX";
            dataToPost.CoolingPeriod = coolingPeriod;
            CommPreferences.Add(new ExpandoObject());
            CommPreferences[0].CategoryCode = "SALES";
            CommPreferences[0].ChannelCode = "CALL";
            CommPreferences[0].Subscribed = false;
            CommPreferences[0].ProductIds = products;

            dataToPost.CommPreferences = CommPreferences;


            string commUrl = "commserviceapi".AppSettings() + "api/unsubscribe/SaveUnsubscriptionStatus";

            ObjHeaders.Add("AppName", "Matrix");
            ObjHeaders.Add("AppKey", "CommToken".AppSettings());
            response = CommonAPICall.PostAPICallWithResult(commUrl, 2000, Newtonsoft.Json.JsonConvert.SerializeObject(dataToPost), string.Empty, ObjHeaders);
        }
        catch (Exception ex)
        {
            error = ex.ToString();
        }
        finally
        {
            dataToPost.unsubscribeLevel = unsubscribeLevel;
            LoggingHelper.LoggingHelper.AddloginQueue("", custID, error, "SaveUnsubscriptionStatusAPI", "LeadPrioritizationBLL", "OneLeadAPI", JsonConvert.SerializeObject(dataToPost), response, RequestTime, DateTime.Now);
        }
        return response;
    }

    public ResponseAPI CallRelease(string LeadId, string UserID, string EmployeeID)
    {
        string strException = string.Empty;
        ResponseAPI _ResponseAPI = new ResponseAPI();

        try
        {
            if (Convert.ToInt64(UserID) > 0)
            {
                Int16 canRelease = LeadPrioritizationDLL.CanReleaseLead(Convert.ToInt64(LeadId), Convert.ToInt64(UserID));
                if (canRelease == 1)
                {
                    UpdateReleaseLog(Convert.ToInt64(LeadId), Convert.ToInt64(UserID), EmployeeID);

                    PriorityModel priorityModel = new PriorityModel() { LeadID = Convert.ToInt64(LeadId), ReleaseStatus = 2, EventType = EventTypeEnum.ReleaseLeads };
                    AddLPDataPointInQueue(priorityModel);
                    _ResponseAPI.status = true;
                }
                else
                {
                    _ResponseAPI.status = false;
                    switch (canRelease)
                    {
                        case 2:
                            _ResponseAPI.message = "Weekly Release limit has been exhausted";
                            break;
                        case 3:
                            _ResponseAPI.message = "Daily limit of the lead has been exhausted";
                            break;
                        case 4:
                            _ResponseAPI.message = "No call Release will be allowed as 4 Unanswered Attempts have happened today. You can connect with the Customer Tomorrow.";
                            break;
                        default:
                            _ResponseAPI.message = "lead can not be released";
                            break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            strException = ex.ToString();
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(LeadId, 0, strException, "CallRelease", "LeadPrioritizationBLL", "Communication", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
        }

        return _ResponseAPI;
    }
    public void UpdateReleaseLog(Int64 LeadId, Int64 UserID, string EmployeeID)
    {
        string comments = string.Empty;
        string employee = string.Empty;

        LeadPrioritizationDLL.UpdateCallReleaseRequestHistory(Convert.ToInt64(LeadId), UserID);

        employee = UserID == 124 ? "System" : EmployeeID;

        comments = "Call Released by " + employee;
        LeadPrioritizationDLL.LogLeadHistory(UserID, Convert.ToInt64(LeadId), comments);
    }
    public List<ReleasePointData> GetReleaseLeads(string UserID, string productID, string RoleID)
    {
        List<ReleasePointData> leads = new List<ReleasePointData>();
        try
        {
            List<PriorityModel> data = LeadPrioritizationDLL.GetWeeklyExpiredLeads(Convert.ToInt64(UserID), Convert.ToInt16(productID));

            leads = (from r in data.AsEnumerable()
                     select new ReleasePointData
                     {
                         LeadID = r.LeadID,
                         CustName = r.CustName,
                         Status = Convert.ToByte(r.ReleaseStatus),
                         EmployeeID = string.Empty,
                         UserName = string.Empty,
                         LeadStatus = Convert.ToString(r.LeadStatus.StatusID),
                         ExpiryDate = r.PrevPolicyExpDate,

                     }).ToList();
        }
        catch (Exception ex)
        {
            return leads;
        }

        return leads;
    }
    private void RemoveNext5LeadsFromLPData(long leadID)
    {
        MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
        IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, Convert.ToInt64(leadID));
        IMongoFields fields = Fields<PriorityModel>.Include(p => p.LeadID, p => p.IsActive);

        var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);
        if (_priorityModel != null)
        {

            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(p => p.IsActive, false);
            objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
        }
    }
    public List<PriorityModel> GetUserAssignedLeads(Int64 UserID)
    {
        List<PriorityModel> lsAssignedLead = new List<PriorityModel>();
        try
        {
            if (UserID == 0)
                return lsAssignedLead;
            DataSet oDataSet = LeadPrioritizationDLL.GetUserAssignedLeads(UserID);
            if (oDataSet != null && oDataSet.Tables.Count > 0)
            {
                lsAssignedLead = (from dr in oDataSet.Tables[0].AsEnumerable()
                                  select new PriorityModel
                                  {
                                      LeadID = dr.Field<Int64>("LeadID"),
                                      CustName = dr.Field<string>("Name"),
                                      LeadStatus = new LeadStatusData() { Status = dr.Field<string>("StatusName") },
                                      User = new userData
                                      {
                                          AssignedOn = dr["ReAssignedon"] == null || dr["ReAssignedon"] == DBNull.Value ? DateTime.MinValue : dr.Field<DateTime>("ReAssignedon"),
                                      },
                                  }).ToList();
            }
        }
        catch (Exception ex)
        {

        }
        return lsAssignedLead;

    }
    public List<PriorityModel> GetStarLeads(Int64 UserId)
    {
        try
        {
            if (UserId == 0)
                return null;
            List<PriorityModel> lstPriorityModel = LeadPrioritizationDLL.GetStarLeads(UserId);
            if (lstPriorityModel != null && lstPriorityModel.Count > 0)
            {
                foreach (PriorityModel lead in lstPriorityModel)
                    lead.LeadStatus.Status = GetLeadStatusName(lead.LeadStatus.StatusID);
            }
            return lstPriorityModel;
        }
        catch (Exception ex)
        {
            return null;
        }

    }
    public List<PriorityModel> GetExpiringLeads(Int64 UserId, byte productID)
    {
        List<PriorityModel> leads = LeadPrioritizationDLL.GetExpiringLeads(UserId, productID);
        DateTime ct = DateTime.Now;
        short ExpriyShowGap = 0;
        try
        {
            var leadproductconstant = LeadPrioritizationDLL.getPriorityConfigByProduct(productID);
            foreach (PriorityModel lead in leads)
            {
                DateTime temDate = DateTime.Now;
                ExpriyShowGap = lead.LeadCreatedOn.Date == ct.Date ? leadproductconstant.TodayCreatedExprTimeGap : leadproductconstant.BeforeTodayCreatedExprTimeGap;
                if (lead.SkippingTime.Year > 1) // means lead was skipped
                    temDate = lead.SkippingTime.AddMinutes(lead.SkipDurationHrs);

                else if (lead.Call != null && lead.Call.NANC_Attempts % 2 == 0)
                    temDate = lead.Call.calltime.AddMinutes(ExpriyShowGap);

                else if (lead.Call != null && lead.Call.NANC_Attempts % 2 == 1)
                    temDate = lead.Call.calltime.AddMinutes(leadproductconstant.InvisibleTimeNANC);

                if (temDate > DateTime.Now)
                    lead.ExpectedAppearingTime = temDate;
                else
                    lead.ExpectedAppearingTime = DateTime.Now;

                lead.LeadStatus.Status = GetLeadStatusName(lead.LeadStatus.StatusID);// adding status
            }
            return leads.OrderByDescending(x => x.PrevPolicyExpDate).ToList();
        }
        catch (Exception ex)
        {
            return null;
        }

    }
    public List<Int64> CheckLeadTimeZone(List<Int64> LeadList)
    {
        List<PriorityModel> _PriorityModel = new List<PriorityModel>();
        bool status;

        _PriorityModel = LeadPrioritizationDLL.GetCountryIdOfLeads(LeadList);
        foreach (PriorityModel data in _PriorityModel)
        {
            status = LeadPrioritizationDLL.IsCorrectTimeToCall(data.Country);
            if (!status && data.ProductID != 117)
            {
                LeadList.Remove(data.LeadID);
            }
        }
        return LeadList;
    }
    public long GetTLCallingNo(string UserId)
    {
        long TLCallingNo = 0;
        TLCallingNo = LeadPrioritizationDLL.GetTLCallingNo(UserId);
        return TLCallingNo;
    }
    public List<PriorityModel> GetLastCalledLeads(Int64 UserId, Int16 productID)
    {
        DataSet oDataSet = LeadPrioritizationDLL.GetLastCalledLeads(UserId, productID);
        List<PriorityModel> Leads = new List<PriorityModel>();

        try
        {
            if (oDataSet != null && oDataSet.Tables.Count > 0)
            {
                foreach (DataRow row in oDataSet.Tables[0].Rows)
                {
                    PriorityModel _PriorityModel = new PriorityModel();
                    CallData oCallData = new CallData();
                    oCallData.calltime = row["CALLDATE"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["CALLDATE"]);
                    _PriorityModel.LeadID = row["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadID"]);
                    _PriorityModel.CustName = row["Name"] == DBNull.Value ? "" : Convert.ToString(row["Name"]);
                    _PriorityModel.ProductID = Convert.ToByte(row["ProductID"]);
                    _PriorityModel.CustID = row["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(row["CustomerID"]);

                    _PriorityModel.Call = oCallData;
                    Leads.Add(_PriorityModel);
                }

            }
            return Leads;
        }
        catch (Exception ex)
        {
            return null;
        }

    }
    public ResponseAPI StarLead(Int64 UserId, Int16 productID, Int64 LeadID, byte Star)
    {
        ResponseAPI _Response = new ResponseAPI();
        try
        {
            if (UserId == 0)
                return _Response;

            _Response = LeadPrioritizationDLL.StarLead(UserId, productID, LeadID, Star);
            return _Response;
        }
        catch
        {
            _Response.status = true;
            return _Response;
        }
    }
    public bool SkipLead(long leadId, Int16 SkipDuration, Int64 Userid)
    {
        string comments = "Lead Skipped For " + SkipDuration + " mins";
        List<long> leads = null;
        DateTime RequestTime = DateTime.Now;
        try
        {
            LeadPrioritizationDLL.SkipLead(leadId, SkipDuration);
            if (oLeadPriorityConstants.SkipChildLead == true)
            {
                leads = GetActiveSetLeads(leadId);// getting all child leads
                foreach (long childlead in leads)
                {
                    // LogSkipHistory(childlead, SkipDuration);
                    LeadPrioritizationDLL.SkipLead(childlead, SkipDuration);

                }
            }

            if (SkipDuration > 10)
            {
                LeadPrioritizationDLL.LogSkipHistory(leadId, SkipDuration, Userid);
                LeadPrioritizationDLL.LogLeadHistory(Userid, leadId, comments);
                LeadPrioritizationDLL.UpdateCallStatusOnCallInitiate(leadId, "Skipped", Userid);
            }
            else
            {
                LeadPrioritizationDLL.UpdateCallStatusOnCallInitiate(leadId, " ", Userid);
            }

        }
        catch (Exception EX)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", leadId, EX.ToString(), "SkipLead", "LeadPrioritizationBLL", "Automation", leads.ToString(), string.Empty, RequestTime, DateTime.Now);
            return false;
        }
        return true;

    }
    public List<long> GetActiveSetLeads(long ParentId)
    {
        List<long> childLeads = new List<long>();
        DataSet oDataSet = LeadPrioritizationDLL.GetActiveSetLeads(ParentId);

        try
        {
            if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow row in oDataSet.Tables[0].Rows)
                    childLeads.Add(Convert.ToInt64(row["ActiveLeadId"]));
            }
        }
        catch (Exception ex)
        {

        }

        return childLeads;

    }
    public int SetRealTimeLeadStatus(LeadCallDetailsDTO _LeadCallDetailsDTO)
    {
        int result = 0;
        DateTime Requesttime = DateTime.Now;
        string strException = string.Empty;
        string Mobile = string.Empty;
        try
        {
            if (!("ManualStampingProds".AppSettings().Contains("," + _LeadCallDetailsDTO.productId + ",") && _LeadCallDetailsDTO.LeadSource.Equals("Renewal")))
            {
                string custConnectTime = LeadPrioritizationDLL.GetCustConnectTime(Convert.ToInt64(_LeadCallDetailsDTO.ParentId));

                if (!string.IsNullOrEmpty(custConnectTime))
                {
                    var diffInSeconds = (DateTime.Now - Convert.ToDateTime(custConnectTime)).TotalSeconds;

                    LeadCallDetailsDTO obj = GetLeadTalkTime(_LeadCallDetailsDTO.ParentId, _LeadCallDetailsDTO.productId, ref Mobile, string.Empty);
                    obj.productId = _LeadCallDetailsDTO.productId;
                    obj.ParentId = _LeadCallDetailsDTO.ParentId;
                    obj.lastNtalktime = Convert.ToInt32(diffInSeconds);
                    Int64 userID = obj.userID;

                    if (obj.Totaltalktime == 0)
                    {
                        obj.Totaltalktime = Convert.ToInt32(diffInSeconds);
                    }
                    //Update LeadStatus in Real Time Basis
                    result = UpdateLeadStatus(obj);

                }

            }
        }
        catch (Exception ex)
        {
            strException = ex.ToString();
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(_LeadCallDetailsDTO.ParentId), _LeadCallDetailsDTO.ParentId, strException, "SetRealTimeLeadStatus", "CommunicationService", string.Empty, string.Empty, string.Empty, Requesttime, DateTime.Now);

        }
        return result;
    }
    public Boolean updateagentstatus(PredictiveAgentStatus obj, bool onCall = false)
    {
        return PredictiveAgentStatusRedis.updateAgentStatusByRedis(obj, onCall);
    }

    public List<RemoveNext5Leads> GetInvalidLeadsFromNext5Leads(RemoveNext5Leads obj)
    {
        DateTime reqTime = DateTime.Now;
        List<RemoveNext5Leads> getNext5Leads = new List<RemoveNext5Leads>();
        UserNext5Leads oUserNext5Leads = null;
        long LeadId = 0;
        try
        {
            if (obj.LeadId > 0)
            {
                LeadId = obj.LeadId;
                DataSet ds = LeadPrioritizationDLL.GetUserIdFromLeadId(obj.LeadId);
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    obj.UserId = ds.Tables[0].Rows[0]["AssignedToUserID"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["AssignedToUserID"]) : 0;
                    obj.UserName = ds.Tables[0].Rows[0]["UserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["UserName"]) : string.Empty;
                    obj.EmpCode = ds.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]) : string.Empty;
                    obj.ParentId = ds.Tables[0].Rows[0]["ParentId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["ParentId"]) : 0;
                }
                if (obj.UserId > 0)
                {
                    oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(obj.UserId);
                    if (oUserNext5Leads.Leads != null && oUserNext5Leads.Leads.Count > 0)
                    {
                        var response = oUserNext5Leads.Leads.FirstOrDefault(e => Convert.ToInt64(e.LeadId) == obj.ParentId);
                        if (response != null)
                        {
                            obj.LeadId = response.LeadId;
                            obj.CustomerName = response.Name;
                            getNext5Leads.Add(obj);
                        }
                        else
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadId, "Lead Not Found!", "GetInvalidLeadsFromNext5Leads", "LeadPrioritizationBLL", "OneLead", "", string.Empty, reqTime, DateTime.Now);
                        }
                    }
                }
            }
            else if (!string.IsNullOrEmpty(obj.EmpCode))
            {
                DataSet ds = LeadPrioritizationDLL.GetUserIdByEmployeeId(obj.EmpCode);
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    obj.UserName = ds.Tables[0].Rows[0]["UserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["UserName"]) : string.Empty;
                    obj.UserId = ds.Tables[0].Rows[0]["AgentId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["AgentId"]) : 0;
                }
                if (obj.UserId > 0)
                {
                    oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(obj.UserId);
                    if (oUserNext5Leads.Leads != null)
                    {
                        LeadId = oUserNext5Leads.Leads[0].LeadId;
                        foreach (var item in oUserNext5Leads.Leads)
                        {
                            var result = new RemoveNext5Leads()
                            {
                                LeadId = item.LeadId,
                                CustomerName = item.Name,
                                UserName = obj.UserName,
                                EmpCode = obj.EmpCode,
                                UserId = obj.UserId
                            };
                            if (result != null)
                            {
                                getNext5Leads.Add(result);
                            }
                        }
                    }
                }

            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "GetInvalidLeadsFromNext5Leads", "LeadPrioritizationBLL", "OneLead", "", string.Empty, reqTime, DateTime.Now);
        }
        return getNext5Leads;
    }
    public bool RemoveInvalidLeadsFromNext5Leads(RemoveNext5Leads obj)
    {
        DateTime reqTime = DateTime.Now;
        UserNext5Leads oUserNext5Leads = new UserNext5Leads();
        UserNext5Leads temp = new UserNext5Leads();
        temp.UserId = obj.UserId;
        temp.Leads = new List<Next5WidgetLead>();
        bool result = false;
        try
        {
            if (obj.UserId > 0 && obj.LeadId > 0)
            {
                oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(obj.UserId);
                var response = oUserNext5Leads.Leads.FirstOrDefault(e => Convert.ToInt64(e.LeadId) == obj.LeadId);


                if (response != null)
                {
                    oUserNext5Leads.Leads.Remove(response);
                    RemoveNext5LeadsFromLPData(obj.LeadId);
                    temp.Leads.Add(response);
                }
                else
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", obj.UserId, "Lead No found!", "RejectNext5Leads", "LeadPrioritizationBLL", "OneLead", "", "", reqTime, DateTime.Now);
                }

                LeadPrioritizationDLL.InsertUpdateNext5Leads(obj.UserId, oUserNext5Leads, false);
                if (temp.Leads.Count > 0)
                {
                    result = true;
                }
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", obj.UserId, ex.ToString(), "RejectNext5Leads", "LeadPrioritizationBLL", "OneLead", "", "", reqTime, DateTime.Now);
        }
        finally
        {
            string objJson = JsonConvert.SerializeObject(temp);
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(obj.RemoveBy), obj.LeadId, objJson, "RejectNext5Leads", "LeadPrioritizationBLL", "OneLead", "", "", reqTime, DateTime.Now);
        }
        return result;
    }

    public static bool ChkRenewalUser(long UserId)
    {
        short processId = 0;
        bool isRenewalGroup = false;


        OneLeadObj oneLeadObj = LeadPrioritizationDLL.GetOneLeadObj(UserId);
        if (oneLeadObj != null)
        {
            processId = oneLeadObj.ProcessId;
        }
        else
        {
            var ds = LeadPrioritizationDLL.GetGroupNameandID(UserId);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                processId = (ds.Tables[0].Rows[0]["ProcessID"] == null || ds.Tables[0].Rows[0]["ProcessID"] == DBNull.Value) ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["ProcessID"]);
            }
        }

        if (processId == 4)
            isRenewalGroup = true;


        return isRenewalGroup;
    }

    private static bool IsRenewalV2_AI(PriorityModel lead, short _GroupID)
    {
        List<short> groupList = new() { 3435, 3458, 2748, 2747, 729, 731 };
        if (lead.LeadID % 2 != 0 && groupList.Contains(_GroupID))
        {
            return true;
        }
        return false;
    }

    public bool ProcessLeadByCategory(RenewalIntentRequest request)
    {
        bool result = false;
        DateTime requestTime = DateTime.Now;
        string error = string.Empty;

        try
        {
            if (request.LeadId > 0 && request.Data != null && request.Data.Count > 0)
            {
                PriorityModel LeadData = LeadPrioritizationDLL.GetLeadCallFreqDetails(request.LeadId);
                if (LeadData != null && LeadData.User != null && IsRenewalV2_AI(LeadData, LeadData.User.GroupId))
                {
                    if (request.Data.ContainsKey("ISSUE"))
                    {
                        result = UpdateLeadCallingFrequency(request, "ISSUE", LeadData);
                        LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "ISSUE", request.CallDataID);

                    }
                    else if (request.Data.ContainsKey("NEAR_EXPIRY"))
                    {
                        result = UpdateLeadCallingFrequency(request, "NEAR_EXPIRY", LeadData);
                        LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "NEAR_EXPIRY", request.CallDataID);

                    }


                    if (request.Data.ContainsKey("CALLBACK") && !string.IsNullOrEmpty(request.Data["CALLBACK"]))
                    {
                        if (LeadData.CallBack == null || LeadData.CallBack.CBtime < DateTime.Now) // Do not change, if callback is already set - Vaibhav Saini
                        {
                            DateTime callbackTime;
                            if (DateTime.TryParse(request.Data["CALLBACK"], out callbackTime))
                            {
                                result = SetAgentCallback(request.LeadId, 124, callbackTime, 0);
                                LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "CALLBACK", request.CallDataID);
                            }
                        }
                        else
                        {
                            DateTime callbackTime;
                            if (DateTime.TryParse(request.Data["CALLBACK"], out callbackTime))
                            {
                                LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "CALLBACK-ALREADY-SET", request.CallDataID);
                            }
                        }
                        result = UpdateLeadCallingFrequency(request, "CALLBACK", LeadData);
                    }

                    if (request.Data.ContainsKey("WHATSAPP_EMAIL"))
                    {
                        result = UpdateLeadCallingFrequency(request, "WHATSAPP_EMAIL", LeadData);
                        LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "WHATSAPP_EMAIL", request.CallDataID);
                        result = true;
                    }
                    if (request.Data.ContainsKey("REGIONAL_LANGUAGE"))
                    {
                        LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "REGIONAL_LANGUAGE", request.CallDataID);
                        result = true;
                    }
                    if (request.Data.ContainsKey("NO_ACTION"))
                    {
                        LeadPrioritizationDLL.LogLeadCallFreqProcess(request.LeadId, "NO_ACTION", request.CallDataID);
                        result = true;
                    }
                }
                else
                {
                    return true;
                }
            }
        }
        catch (Exception ex)
        {
            error = ex.ToString();
            LoggingHelper.LoggingHelper.AddloginQueue(
                "",
                request.LeadId,
                error,
                "ProcessLeadByCategory",
                "LeadPrioritizationBLL",
                "OneLeadPriority",
                JsonConvert.SerializeObject(request),
                result.ToString(),
                requestTime,
                DateTime.Now
            );
            return false;
        }
        finally {
            LeadPrioritizationDLL.InsertRenewalCallAnalysis(request);
        }
        return result;
    }


    private static bool UpdateLeadCallingFrequency(RenewalIntentRequest request, string frequencyProcess, PriorityModel LeadData)
    {
        // Get the current frequency process
        string currentFrequency = string.Empty;
        if (LeadData != null)
        {
            currentFrequency = LeadData.CallFreqProcess;
        }
        if (string.IsNullOrEmpty(currentFrequency) ||
            currentFrequency != "ISSUE" || frequencyProcess == "ISSUE")
        {
            LeadPrioritizationDLL.UpdateCallFreqProcess(request.LeadId, frequencyProcess);
            return true;
        }

        return false;
    }


    public static bool SetAgentCallback(long LeadID, long UserId, DateTime dt, byte IsGoogleInvite, bool issmeTL = false, string EmailId = "")
    {
        string strException = string.Empty;
        string res = string.Empty;
        var json = "";
        try
        {
            string starttime = dt.ToString("MM/dd/yyyy HH:mm");
            string endtime = dt.AddMinutes(3).ToString("MM/dd/yyyy HH:mm");

            var url = "MatrixApiUrl".AppSettings();

            var headerParams = new Dictionary<string, string>
                 {
                    { "source", "matrix" },
                    { "authKey", "MatrixAuthKey".AppSettings() },
                    { "clientKey",  "MatrixClientKey".AppSettings() }
                 };

            
            json = "{\"AgentId\": \"" + UserId + "\",\"Id\": \"" + LeadID + "\",\"Subject\": \"" + "CustomerAgreed." + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime.Replace("-", "/") + "\",\"EndDate\": \"" + endtime.Replace("-", "/") + "\",\"CallBackTypeId\": 2,\"IsPaymentCallback\": false,\"AdminId\": \"" + UserId + "\",\"IsCore\": false,\"NeedId\": 0,\"NeedStatusId\": 0,\"IsGoogleInvite\": false  } ";
            

            url = url + "coremrs/api/CallBackSchedular/InsertCallSchedulerEvent";
            res = CommonAPICall.PostAPICallWithResult(url, 2000, json, string.Empty, headerParams);
        }
        catch (Exception ex)
        {
            strException = ex.ToString();
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "SetAgentCallback1", "Onelead", "SetAgentCallback", json, res, DateTime.Now, DateTime.Now);
        }
        return true;
    }
}

