using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Helper;
using System.Runtime.Caching;
using Newtonsoft.Json;
using System.Net;
using System.Text;

namespace Redis
{
    public class RedisHelper
    {
        static readonly object _obj = new();
        static readonly object Identity = new();
        private static readonly List<ConnectionMultiplexer> _redisList = new();
        private static readonly int poolSize = 10;

        static IDatabase GetDatabase()
        {
            int index = new Random().Next(0, poolSize);
            ConnectionMultiplexer connection = _redisList.ElementAtOrDefault(index);

            if (_redisList == null || _redisList.Count == 0 || connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
            {
                lock (_obj)
                {
                    //connection = _redisList.ElementAtOrDefault(index);
                    if (connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
                    {
                        if (connection != null)
                            _redisList.RemoveAt(index);

                        IConfiguration con = Custom.ConfigurationManager.AppSetting;
                        //string Enviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString();

                        string Enviornment = CoreCommonMethods.GetEnvironmentVar().ToUpper();
                        List<string> nodes = con.GetSection("Communication").GetSection("RedisConnection").GetSection(Enviornment).Value.Split(',').ToList();
                        string AWSSecretEnv = con.GetSection("Communication").GetSection("AWSSecretEnvironment").GetSection(Enviornment).Value.ToString();
                        string RedisPass = GetSecretKeyFromAWS("RedisCustomerRevisitPass", AWSSecretEnv.ToString());
                        ConfigurationOptions option = new()
                        {
                            AbortOnConnectFail = false,
                            ConnectTimeout = 1000,
                            SyncTimeout = 2000,
                            ConnectRetry = 1,
                            Password = RedisPass,
                            // Ssl = true
                        };

                        foreach (var node in nodes)
                        {
                            option.EndPoints.Add(node);
                        }

                        connection = ConnectionMultiplexer.Connect(option);
                        _redisList.Add(connection);
                    }
                }
            }

            return connection.GetDatabase();
        }
        public static string GetRedisData(string Key)
        {
            IDatabase db = GetDatabase();
            string data = db.StringGet(Key, CommandFlags.PreferReplica);
            return data;
        }

        public static void SetRedisData(string Key, string Value, TimeSpan timeSpan)
        {
            IDatabase db = GetDatabase();
            db.StringSet(Key, Value);
            db.KeyExpire(Key, timeSpan);
        }

        public static void SetRedisData(string Key, string Value)
        {
            IDatabase db = GetDatabase();
            db.StringSet(Key, Value);
        }

        public static void MSetRedisData(Dictionary<string, string> KeyValueList)
        {
            IDatabase db = GetDatabase();
            if (KeyValueList.Any())
            {
                int size = KeyValueList.Count;
                var set = from item in KeyValueList
                          select new KeyValuePair<RedisKey, RedisValue>(item.Key, item.Value);
                if (set.Any())
                    db.StringSet(set.ToArray());
            }

        }

        public static void SetZAdd(string Key, string Value, TimeSpan timeSpan)
        {
            IDatabase db = GetDatabase();
            long sort = Convert.ToInt64(DateTime.Now.ToString("yyyyMMddHHmmss"));
            db.SortedSetAdd(Key, Value, sort);
            db.KeyExpire(Key, timeSpan);
        }
        public static string GetSecretKeyFromAWS(string Key, string SecretEnvironment)
        {
            string EnvObj = string.Empty;
            string Value = string.Empty;
            StringBuilder sb = new StringBuilder();
            DateTime Requesttime = DateTime.Now;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Enviornment = CoreCommonMethods.GetEnvironmentVar().ToUpper();
            try
            {
                string IsAWSSceretEnabled = con.GetSection("Communication").GetSection("IsAWSSceretEnabled").GetSection(Enviornment).Value.ToString();
                if (CoreCommonMethods.IsValidString(IsAWSSceretEnabled) && Convert.ToBoolean(IsAWSSceretEnabled))
                {
                    if (MemoryCache.Default[SecretEnvironment] != null)
                    {
                        EnvObj = Convert.ToString(MemoryCache.Default[SecretEnvironment]);
                    }
                    else
                    {
                        lock (Identity)
                        {
                            if (MemoryCache.Default[SecretEnvironment] != null)
                            {
                                EnvObj = Convert.ToString(MemoryCache.Default[SecretEnvironment]);
                            }
                            else
                            {
                                sb.Append(" ==== read secret === ");
                                sb.Append(" ==== IPAddress === " + GetIPAddress());
                                sb.Append(" ,SecretEnvironment" + SecretEnvironment);
                                if (!string.IsNullOrEmpty(SecretEnvironment))
                                { EnvObj = AmazonSecret.GetSecret(SecretEnvironment); }
                                if (CoreCommonMethods.IsValidString(EnvObj))
                                {
                                    sb.Append(" ====  Insert Cache=== " + EnvObj);
                                    CommonCache.GetOrInsertIntoCache(EnvObj, SecretEnvironment, 12 * 60);
                                }
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(EnvObj))
                    {
                        sb.Append("=========Enter ConnectionObj  is not empty===========");
                        dynamic obj = JsonConvert.DeserializeObject(EnvObj);
                        if (obj != null)
                        {
                            if (Key.ToLower() == "rediscustomerrevisitpass")
                                Value = obj.RedisCustomerRevisitPass;
                        }
                    }
                    if (string.IsNullOrEmpty(Value))
                    {
                        Value = con.GetSection("Communication").GetSection("RedisPassword").GetSection(Enviornment).Value.ToString();
                    }
                }
                else
                {
                    Value = con.GetSection("Communication").GetSection("RedisPassword").GetSection(Enviornment).Value.ToString();
                }
            }
            catch (Exception ex)
            {
                Value = con.GetSection("Communication").GetSection("RedisPassword").GetSection(Enviornment).Value.ToString();
            }
            return Value.ToString();
        }
        public static string GetIPAddress()
        {
            string ipaddress = string.Empty;
            try
            {
                string strHostName = System.Net.Dns.GetHostName();
                IPHostEntry ipHostInfo = Dns.GetHostEntry(strHostName);
                IPAddress ipAddress = ipHostInfo.AddressList[0];
                ipaddress = ipAddress.ToString();
            }
            catch (Exception ex)
            {
            }
            return ipaddress;
        }


    }
}