﻿{
  "lstLeadPointDetails": [
    {
      "LeadPointType": "NewLead",
      "Points": 30
    },
    {
      "LeadPointType": "PaymentCB",
      "Points": 0
    },
    {
      "LeadPointType": "Revisit",
      "Points": 3
    },
    {
      "LeadPointType": "NotAnswered",
      "Points": -3,
      "Count": 2
    },
    {
      "LeadPointType": "Answered",
      "Points": 18,
      "TalkTime": 5
    },
    {
      "LeadPointType": "CallBack",
      "Points": 0
    }
  ],
  "RevisitMintsGap": 15,
  "MorningShift": 0,
  "AfterNoonShift": 13,
  "EveningShift": 16,
  "SalesRejStatus": [ 5, 6, 7, 12, 13, 14 ],
  "BookedStatus": [ 13 ],
  "pooleventqueuename": "pooleventqueue",
  "pullNewAssignmentInterval": 2000,
  "priorityEventqueue": "priorityeventqueue",
  "ReadPriorityEventInterval": 1000,
  "DispositionQueueConstant": {
    "readDispositionqueueInterval": 1000,
    "dispositionQueueName": "dispositionqueue",
    "dispositionQueueAlertMSMQCount": 0,
    "dispositionQueueAlertMail": false,
    "DispositionQueueReadLog": true
  },
  "DeductNANCLog": true,
  "ReadPullEventLog": true,
  "ProcessEventLog": true,
  "PriorityGroups": [ 508, 457, 337, 671, 687, 668, 670, 689, 690, 691, 297, 623, 693, 624, 593, 652, 669, 692, 700, 225, 706, 228, 224, 719, 200, 280, 617, 233, 272, 225, 279, 718, 327, 230, 224, 302, 231, 603, 601, 277, 303, 228, 226, 221, 680, 41, 642, 141, 179, 708, 112, 707, 39, 55, 42, 185, 195, 64, 641, 643, 180, 132, 105, 181, 133, 44, 694, 441, 709, 710, 711, 134, 735, 739, 740, 741, 742, 781, 755, 758, 743, 757, 745, 748, 746, 749, 756, 759, 304, 807, 596, 747, 188, 809, 810, 811, 812, 762, 595, 767, 753, 760, 750, 768, 751, 763, 761, 770, 754, 772, 771, 656, 765, 773, 764, 766, 808, 826, 627, 715, 829, 827, 830, 831 ],
  "prioritylogicon": true,
  "PriorityEventQueueEntryOn": false,
  "MatrixStatus": [ 1, 2, 3, 4, 11 ],
  "DeductNAPointsInterval": 60000,
  "TimeDifferenceDailerNTata": 300,
  "NriIdentification": [ "91", "0", "392", "india", "+91" ],
  "ReOpenPoint": 6,
  "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
  "Disposition": {
    "Answered": "ANSWERED",
    "NoAnswer": "NO ANSWER",
    "Busy ": "BUSY",
    "Failed": "FAILED",
    "Congestion": "CONGESTION"
  },
  "PooleventQExpMinutes": 1,
  "PriorityeventQExpMinutes": 0,
  "PriorityLogConstant": {
    "ReadPriorityLogInterval": 5000,
    "ReadPriorityLogLog": false,
    "PriorityLogQueueName": "prioritylogqueue",
    "PriorityLogQueueEntryOn": true,
    "IsLogSqlDumpOn": true,
    "IsPositionLogSqlDumpOn": true,
    "IsPositionLogMongoDumpOn": false
  },
  "msg": {
    "PaymentCB": "Payment CallBack",
    "ActiveRevisit": "Customer Revisit",
    "ActiveNew": "New Lead",
    "ActiveCB": "Call Back ",
    "PassiveCB": "Call Back",
    "PassiveRevisit": "Customer Revisit",
    "PassiveNew": "New Lead",
    "SecondAttemptPCB": "Call Back(2)",
    "SecondAttemptActvCB": "Call Back(2)",
    "SecondAttemptActvNew": "New Lead(2)",
    "SecondAttemptActvRevisit": "Customer Revisit(2)",
    "SecondAttemptPasvCB": "Call Back(2)",
    "SecondAttemptPasvNew": "New Lead(2)",
    "SecondAttemptPasvRevisit": "Customer Revisit(2)",
    "UnansweredLeads": "Unanswered Lead",
    "RestLeads_1": "System CallBack",
    "RestLeads_2": "System CallBack",
    "SecondAttemptRestLeads": "System CallBack(2)",
    "CallAllowedAfterFewMinutes": "",
    "CallReleasedLeads": "Call Released Lead",
    "RecentExpriyLeads": "Recent Expiry",
    "RevisitMsg": {
      "Website": "Via Website",
      "Inbound": "Via Inbound",
      "EmailReply": "Via EmailReply"
    },
    "SkippedLeads": "Previously Skipped",
    "FutureCallBackLeads": "Future CallBack",
    "ImportantLeadLimitMsg": "Limit of marking lead as IMPORTANT is OVER. Please mark few leads as UNIMPORTANT!",
    "BdayLeads": "BirthDay Lead",
    "PaymentFailure": "Payment Failure",
    "SecondAttemptPaymentFailure": "Payment Failure(2)",
    "EmailRevisit": "Email Revisit",
    "SecondAttemptEmailRevisit": "Email Revisit(2)",
    "CTCRevisit": "CTC Revisit",
    "SecondAttemptRevisitCTC": "CTC Revisit(2)",
    "BajajCustomerRevisit": "Bajaj Revisit",
    "MissedCB": "Missed CallBack",
    "BookedCB": "BookedCB",
    "TicketUpdate": "TicketUpdate",
    "StatusChange": "StatusChange",
    "NoLeadPopup": "NoLeadPopup",
    "ProposalError": "ProposalError",
    "QuoteShared": "QuoteShared",
    "RestPriorityLeads": "System CallBack",
    "RevisionShared": "RevisionShared",
    "TodayExpiry": "TodayExpiry",
    "SecondAttemptTodayExpiry": "TodayExpiry(2)",
    "PFFilled": "PF Filled",
    "SecondAttemptPFFilled": "PF Filled(2)",
    "RMLeads": "RMLeads",
    "CancelBookedLead": "CancelBookedLead",
    "ServiceCB": "ServiceCallBack",
    "VisitToday": "VisitToday",
    "SecondAttemptVisitToday": "VisitToday(2)",
    "ConfirmVisit": "ConfirmVisit",
    "SecondAttemptConfirmVisit": "ConfirmVisit(2)",
    "MissedVisit": "MissedVisit",
    "SecondAttemptMissedVisit": "MissedVisit(2)",
    "VisitUpdate": "VisitUpdate",
    "SecondAttemptVisitUpdate": "VisitUpdate(2)",
    "FOSChurn": "FOSChurn",
    "SecondAttemptFOSChurn": "FOSChurn(2)",
    "CustomerCB": "Customer Scheduled CB"
  },
  "CallReleaseCount": 1,
  "Releaseleadsshowtime": 2800,
  "RevisitThreadWait": 2,
  "RevisitOrPaymentTimeMorning": 900,
  "RevisitOrPaymentTimeAfterMorning": 60,
  "SkipChildLead": true,
  "StarLeadsLimit": 20,
  "SkipBucketGapMints": 60,
  "SkipBucketCallGapMints": 60,
  "RemoveChildLdInterval": 3600000,
  "BookedInterval": 60,
  "LastDaysCount": 4,
  "AvgUserCallPerDay": 40,
  "RecentExpiryGap": 2,
  "MatrixBookedStatus": [ 13, 37, 39, 41, 77 ],
  "PolicyIssuedStatus": [ 42, 43, 44 ],
  "CommonPoolInterval": 45000,
  "LogoutInterval": 90000,
  "UpdateChatAssignInterval": 300000,
  "MrngstartTime": "07:00",
  "MrngEndTime": "07:05",
  "NgtstartTime": "21:00",
  "NgtEndTime": "21:05",
  "MrngChatLimit": 10,
  "NgtChatLimit": 0,
  "ChatRoasterAllocationInterval": 3600000,
  "SyncChatProcessDataInterval": 30000,
  "PriorityConstant": [
    {
      "productID": 117,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "TODAYEXPIRY", "PAYMENTFAILURE", "PROPOSALERROR", "2ndAttemptPAYMENTFAILURE", "RMLEAD", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRY", "Unanswered", "ANSWERED", "REST" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 115,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 30,
      "prioritySequence": [ "TODAYVISIT", "2NDATTEMPTTODAYVISIT", "SERVICECB", "BAJAJREVISIT", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "CUSTOMERCB", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "FOSCHURN", "2NDATTEMPTFOSCHURN", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "CONFIRMVISIT", "2NDATTEMPTCONFIRMVISIT", "Unanswered", "ANSWERED", "VISITUPDATE", "2NDATTEMPTVISITUPDATE", "MISSEDVISIT", "2NDATTEMPTMISSEDVISIT", "REST" ],
      "bookedPrioritySequence": [ "CALLBACK", "TICKET", "STATUSCHANGE", "UNANSWERED", "REST" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 7,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90,
      "VisitGroups": [2077]
    },
    {
      "productID": 7,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "TODAYVISIT", "2NDATTEMPTTODAYVISIT", "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "RMLEAD", "CUSTOMERCB", "PaymentCB", "2ndAttemptPCB", "BOOKEDCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "FOSCHURN", "2NDATTEMPTFOSCHURN", "ActiveNew", "2ndAttemptActiveNew", "SOSDOCTICKETS", "PFFILLED", "2NDATTEMPTPFFILLED", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "BDAY", "CONFIRMVISIT", "2NDATTEMPTCONFIRMVISIT", "Unanswered", "ANSWERED", "VISITUPDATE", "2NDATTEMPTVISITUPDATE", "MISSEDVISIT", "2NDATTEMPTMISSEDVISIT", "REST", "SOSREST" ],
      "BdayRange": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 7,
      "MaxAttempts": 25,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 64, 134, 161, 641, 743, 836, 1089, 1097, 1106, 1107, 1139, 1143, 1177, 1240 ],
      "TodayExpCall": [ "2020-03-10" ],
      "PercentileVal": "0.95",
      "IssuedAPEWeigthage": "75",
      "APE_Percentage": "0.8",
      "Quality_Score": "66",
      "Quailty_Percentage": "0.7",
      "Quailty_Weigthage": "5",
      "Quiz_Weigthage": "5",
      "Quiz_Percentage": "0.7",
      "TT_Percentage": "0.7",
      "TT_Weigthage": "15",
      "SOSGroups": [ 1103, 708, 39, 1099, 709, 106, 1101, 710, 711, 64, 132, 1228, 1663, 743, 133, 161, 134 ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90,
      "AIRestUsers": [ 19, 29, 1863, 2301, 2650, 2728, 2884, 2909, 3451, 4035, 5636, 6346, 7035, 7287, 7550, 7737, 7738, 8263, 8367, 8374, 9001, 9862, 10045, 10117, 10197, 10376, 10427, 10808, 10910, 11309, 11563, 11636, 11699, 12066, 12421, 12573, 12626, 12824, 12910, 12956, 13085, 13092, 13319, 13320, 13523, 13566, 13589, 14023, 14080, 14100, 14193, 14294, 14392, 14441, 14603, 14689, 14730, 14768, 14780, 14907, 15060, 15797, 15884, 15892, 16090, 16288, 16541, 16543, 16556, 16812, 16822, 16858, 16963, 16969, 17098, 17110, 17122, 17132, 17137, 17181, 17182, 17184, 17196, 17218, 17233, 17246, 17585, 17724, 17731, 17887, 17896, 17910, 17937, 18275, 18539, 18569, 18702, 18714, 18716, 18718, 18766, 19152, 19174, 19341, 19459, 19721, 19786, 19956, 19968, 19994, 19995, 20012, 20022, 20203, 20229, 20286, 20330, 20336, 20797, 20886, 20887, 20999, 21121, 21172, 21202, 21203, 21607, 21714, 21736, 21858, 21899, 22075, 22147, 22209, 22222, 22380, 22415, 22623, 22629, 22644, 23352, 23366, 23658, 23681, 23692, 23747, 23759, 23760, 23854, 23963, 23972, 24087, 24096, 24105, 24231, 24286, 24291, 24302, 24387, 24411, 24486, 24494, 24498, 24499, 24520, 24582, 24584, 24601, 24824, 24943, 24964, 24973, 25121, 25126, 25129, 25137, 25145, 25152, 25159, 25303, 25538, 26149, 26150, 26156, 26264, 26333, 26339, 26353, 26415, 26417, 26420, 26425, 26586, 26592, 26744, 26850, 26872, 26881, 27033, 27035, 27046, 27220, 27316, 27348, 27364, 27365, 27406, 27429, 27534, 27545, 27550, 27603, 27607, 27663, 27733, 27774, 27775, 27949, 27968, 27971, 28024, 28036, 28407, 28409, 28590, 28591, 28732, 28764, 29102, 29105, 29110, 29461, 29646, 29685, 29692, 29804, 29906, 29916, 29985, 30005, 30069, 30113, 30175, 30285, 30304, 30381, 30416, 30418, 30420, 30421, 30423, 30521, 30534, 30536, 30566, 30572, 30597, 30603, 30662, 30673, 30699, 30714, 30747, 30764, 30913, 30915, 30918, 30926, 30929, 30930, 31080, 31089, 31135, 31170, 31175, 31176, 31188, 31203, 31206, 31208, 31210, 31280, 31283, 31363, 31370, 31389, 31453, 31487, 31546, 31562, 31563, 31565, 31566, 31574, 31612, 31625, 31628, 31638, 31650, 31716, 31739, 31746, 31789, 31909, 31913, 31941, 32111, 32112, 32113, 32123, 32134, 32139, 32192, 32196, 32200, 32201, 32342, 32348, 32354, 32362, 32378, 32381, 32390, 32398, 32403, 32410, 32420, 32567, 32640, 32647, 32655, 32759, 32763, 32767, 32781, 32791, 32793, 32799, 32976, 32982, 32987, 32990, 32992, 32994, 32996, 33000, 33002, 33101, 33103, 33104, 33143, 33145, 33151, 33159, 33163, 33169, 33171, 33174, 33179, 33185, 33187, 33193, 33200, 33249, 33271, 33273, 33275, 33278, 33282, 33285, 33286, 33288, 33289, 33292, 33293, 33300, 33305, 33306, 33308, 33310, 33312, 33356, 33360, 33361, 33363, 33367, 33375, 33376, 33377, 33629, 33681, 33682, 33684, 33688, 33690, 33700, 33703, 33743, 33745, 33747, 33750, 33753, 33755, 33759, 33760, 33770, 33778, 33783, 33788, 33794, 33795, 33806, 33811, 33814, 33819, 33824, 33890, 33891, 33894, 33895, 33896, 33899, 33901, 33907, 33910, 33913, 33916, 33918, 33920, 33923, 33943, 34288, 34292, 34294, 34297, 34299, 34300, 34302, 34303, 34305, 34307, 34308, 34325, 34329, 34330, 34332, 34333, 34337, 34343, 34351, 34352, 34435, 34436, 34437, 34439, 34441, 34443, 34445, 34448, 34450, 34451, 34468, 34469, 34470, 34472, 34473, 34475, 34477, 34479, 34487, 34489, 34492, 34493, 34494, 34496, 34498, 34503, 34509, 34511, 34541, 34705, 34707, 34710, 34713, 34715, 34717, 34719, 34728, 34731, 34733, 35101, 35102, 35109, 35111, 35114, 35116, 35117, 35118, 35119, 35121, 35124, 35126, 35129, 35133, 35134, 35136, 35138, 35139, 35140, 35142, 35144, 35146, 35147, 35148, 35150, 35151, 35152, 35154, 35157, 35159, 35160, 35161, 35229, 35231, 35233, 35235, 35237, 35239, 35243, 35245, 35248, 35250, 35251, 35254, 35256, 35258, 35259, 35261, 35263, 35265, 35267, 35269, 35271, 35274, 35276, 35277, 35279, 35281, 35316, 35344, 35352, 35355, 35737, 35739, 35747, 35752, 35757 ],
      "VisitGroups": [ 1109 ]
    },
    {
      "productID": 2,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "TODAYVISIT", "2NDATTEMPTTODAYVISIT", "SERVICECB", "FOSCHURN", "2NDATTEMPTFOSCHURN", "ActiveNew", "2ndAttemptActiveNew", "CUSTOMERCB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "RMLEAD", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "PassiveNew", "2ndAttemptPassiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "CONFIRMVISIT", "2NDATTEMPTCONFIRMVISIT", "Unanswered", "ANSWERED", "VISITUPDATE", "2NDATTEMPTVISITUPDATE", "MISSEDVISIT", "2NDATTEMPTMISSEDVISIT", "REST" ],
      "bookedPrioritySequence": [ "CALLBACK", "TICKET", "STATUSCHANGE", "UNANSWERED", "REST" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 25,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "AIRestUsers": [ 15707, 16462, 19700, 20514, 21362, 24755, 24859, 26099, 29715, 29788, 7768, 8063, 4090, 5602, 9641, 9883, 11955, 12944, 13047, 13923, 14009, 14004, 14259, 14465, 14683, 14946, 15402, 15927, 16120, 16442, 16687, 16704, 18659, 19076, 20602, 21535, 22268, 22483, 22699, 22882, 22940, 23036, 23479, 24149, 24175, 24866, 25579, 26373, 26283, 26794, 23199, 23008, 23027, 22999, 17989, 17668, 17658, 17648, 17617, 17854, 17567, 17032, 16744, 16718, 16715, 16691, 16595, 16418, 16407, 16375, 15925, 15712, 15972, 15367, 15097, 15021, 15020, 14878, 14868, 14681, 14575, 14523, 14512, 14509, 14454, 14451, 14122, 13481, 13460, 13254, 12723, 12269, 12187, 12134, 11945, 11091, 10891, 10831, 10514, 10277, 9886, 9712, 9704, 9560, 9392, 9254, 9099, 7155, 5925, 5861, 5291, 5082, 7679, 31152, 30943, 30246, 29710, 29711, 29538, 29035, 28643, 28642, 28500, 28509, 28256, 27767, 27720, 27235, 27023, 26821, 26289, 26114, 25924, 25802, 25600, 25787, 25554, 25194, 25190, 25043, 25213, 24754, 24330, 24394, 24318, 24033, 23996, 23991, 23833, 23895, 23487, 23469, 23461, 23459, 23471, 23315, 23189, 25324, 22873, 22892, 22702, 22676, 22450, 22464, 22487, 22273, 22276, 22263, 22188, 22062, 22064, 21966, 21930, 21939, 21658, 21646, 21522, 21539, 21557, 21377, 21084, 20903, 20898, 20900, 20578, 20568, 20513, 20417, 20404, 20221, 19880, 19701, 19645, 19478, 19312, 19303, 19066, 18607, 18466 ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90,
      "VisitGroups": [ 2004 ]
    },
    {
      "productID": 2,
      "subProduct": "RENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      //"prioritySequence": [ "REVISITCTC", "SERVICECB", "PaymentCB", "PAYMENTFAILURE", "2ndAttemptPCB", "2ndAttemptPAYMENTFAILURE", "SAMEDAYEXPIRY", "EmailRevisit", "ACTIVEREVISIT", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "MissedCB", "EXPIRY-707DAYS", "EXPIRY0815DAYS", "EXPIRY-15-8DAYS", "EXPIRY-30-15DAYS", "EXPIRY-60-30DAYS", "EXPIRY1630DAYS", "EXPIRY31>DAYS", "SAMEDAYEXPIRY-R5", "EXPIRY-707DAYS-R5", "EXPIRY0815DAYS-R5", "EXPIRY-60-8DAYS-R5", "EXPIRY1630DAYS-R5", "EXPIRY31>DAYS-R5", "REST", "CallRelease", "PassiveNew" ],
      //"prioritySequence": [ "REVISITCTC", "SERVICECB", "PaymentCB", "PAYMENTFAILURE", "2ndAttemptPCB", "2ndAttemptPAYMENTFAILURE", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EmailRevisit", "ACTIVEREVISIT", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "MissedCB", "EXPIRY0110DAYS", "GRACE0110DAYS", "EXPIRY1045DAYS", "GRACE2560DAYS", "GRACE1025DAYS", "EXPIRY4560DAYS", "REST", "CallRelease", "PassiveNew" ],
      "prioritySequence": [ "REVISITCTC", "SERVICECB", "PaymentCB", "PAYMENTFAILURE", "2ndAttemptPCB", "2ndAttemptPAYMENTFAILURE", "TODAYEXPIRY", "EXPIRYLOGIC", "2NDATTEMPTTODAYEXPIRY", "EmailRevisit", "ACTIVEREVISIT", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "MissedCB", "EXPIRYLOGIC1", "EXPIRY0110DAYS", "GRACE0110DAYS", "EXPIRY1045DAYS", "GRACE2560DAYS", "GRACE1025DAYS", "EXPIRY4560DAYS", "REST", "PassiveNew" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 28,
      "WeekMaxAttempts": 28,
      "MaxAttempts": 100,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "PortGroups": [ 1504 ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 2,
      "subProduct": "PAYMENT",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      //"prioritySequence": [ "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "TODAYEXPIRY", "SecondAttemptTodayExpiry", "GRACE0105DAYS", "SECONDATTEMPTGRACE0105DAYS", "GRACE1215DAYS", "SECONDATTEMPTGRACE1215DAYS", "GRACE0512DAYS", "GRACE1530DAYS", "GRACE30120DAYS", "CallRelease" ],
      "prioritySequence": [ "REVISITCTC", "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "EXPIRYLOGIC"],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 28,
      "WeekMaxAttempts": 28,
      "MaxAttempts": 100,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "PortGroups": [ 1504 ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 3,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "InvisibleTimeNANC_PaymentFailure": 5,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "EmailRevisit", "2ndAttemptEmailRevisit", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "Unanswered", "ANSWERED", "REST"],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 114,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRY", "Unanswered", "ANSWERED", "REST" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 154,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRY", "Unanswered", "ANSWERED", "REST" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 117,
      "subProduct": "RENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "ACTIVEREVISIT", "2NDATTEMPTACTIVEREVISIT", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "PASSIVEREVISIT", "2NDATTEMPTPASSIVEREVISIT", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EXPIRY0108DAYS", "ActiveCB", "EXPIRY0812DAYS", "EXPIRY1230DAYS", "2ndAttemptActiveCB", "REST", "PassiveCB", "2ndAttemptPassiveCB", "EXPIRY3045DAYS", "PassiveNew", "MissedCB", "Unanswered" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 10,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90

    },
    {
      "productID": 131,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 30,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "QUOTEREVISIT", "REVISIONREVISIT", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveCB", "2ndAttemptActiveCB", "ActiveNew", "2ndAttemptActiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveRevisit", "2ndAttemptPassiveRevisit", "QUOTEREST", "REST", "Unanswered" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 20,
      "WeekMaxAttempts": 10,
      "MaxAttempts": 40,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 131,
      "subProduct": "RENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 30,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "QUOTEREVISIT", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveRevisit", "2ndAttemptPassiveRevisit", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EXPIRY0107DAYS", "EXPIRY0815DAYS", "EXPIRY1630DAYS", "EXPIRY3145DAYS", "GRACE0107DAYS", "GRACE0890DAYS", "Unanswered"],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 15,
      "WeekMaxAttempts": 15,
      "MaxAttempts": 100,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 101,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 30,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "QUOTEREVISIT", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveCB", "2ndAttemptActiveCB", "ActiveNew", "2ndAttemptActiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveRevisit", "2ndAttemptPassiveRevisit", "REST", "Unanswered" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 139,
      "subProduct": "",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "REVISITCTC", "EmailRevisit ", "2ndAttemptEmailRevisit", "ActiveNew", "2ndAttemptActiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRY", "Unanswered", "REST" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 117,
      "subProduct": "CHATRENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "ACTIVEREVISIT", "2NDATTEMPTACTIVEREVISIT", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "ActiveCB", "2ndAttemptActiveCB", "PASSIVEREVISIT", "2NDATTEMPTPASSIVEREVISIT", "PassiveCB", "2ndAttemptPassiveCB", "PassiveNew", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EXPIRY0104DAYS", "EXPIRY0412DAYS", "EXPIRY1230DAYS", "EXPIRY3045DAYS", "REST", "MissedCB", "Unanswered" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 117,
      "subProduct": "CHATIBRENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "PaymentCB", "EmailRevisit", "ActiveCB", "PassiveCB", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EXPIRY0107DAYS", "EXPIRY0715DAYS", "EXPIRY1530DAYS", "EXPIRY3045DAYS", "REST", "MissedCB", "Unanswered" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 101,
      "subProduct": "RENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 30,
      "prioritySequence": [ "SERVICECB", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "PaymentCB", "2ndAttemptPCB", "QUOTEREVISIT", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "ActiveCB", "2ndAttemptActiveCB", "ActiveNew", "2ndAttemptActiveNew", "PassiveCB", "2ndAttemptPassiveCB", "PassiveNew", "2ndAttemptPassiveNew", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRYLOGIC" , "REST", "Unanswered" ],
      "RecentExpiryGap": 2,
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 2,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    },
    {
      "productID": 2,
      "subProduct": "FOS",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "ActiveNew", "2ndAttemptActiveNew", "REVISITCTC", "2NDATTEMPTREVISITCTC", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "RMLEAD", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "2ndAttemptEmailRevisit", "ACTIVEREVISIT", "2ndAttemptActiveRevisit", "PassiveNew", "2ndAttemptPassiveNew", "ActiveCB", "2ndAttemptActiveCB", "PassiveCB", "2ndAttemptPassiveCB", "PassiveRevisit", "2ndAttemptPassiveRevisit", "EXPIRY", "Unanswered", "REST" ],
      "bookedPrioritySequence": [ "CALLBACK", "TICKET", "STATUSCHANGE", "UNANSWERED", "REST" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 25,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "AIRestUsers": [ 0 ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90

    },
    {
      "productID": 139,
      "subProduct": "RENEWAL",
      "PriorityQueueSize": 5,
      "LastNANCMintsGap": 90,
      "PCBLastNANCMintsGap": 150,
      "RevisitMintsGap": 15,
      "TalkTimeMin": 59,
      "TalkTimeMax": 299,
      "PntStndrdVal": 12,
      "MinDeductPnt": 3,
      "PaymentCBShowTime": -5,
      "ActiveNewLeadShowTime": 30,
      "ActiveCBShowTime": 5,
      "ActiveRevisitShowTime": 30,
      "InvisibleTimeNANC": 15,
      "InvisibleTimeNANC_Rest": 150,
      "DeductNANCLog": true,
      "ReadPullEventLog": true,
      "ProcessEventLog": true,
      "prioritylogicon": true,
      "RestLeadLastCall": 72,
      "PriorityEventQueueEntryOn": false,
      "MatrixStatus": [ 1, 2, 3, 4, 11 ],
      "NANCMaxAttemInDiffShifts": 4,
      "NANCLastCallDayGap": 1,
      "NANCMax1DayAttempt": 4,
      "DeductNAPointsInterval": 600000,
      "callnotAllowedInterval": 20,
      "TimeDifferenceDailerNTata": 300,
      "ReleasePoint": 6,
      "SKipLeadTimeGap": 60,
      "RestLeadCallMintsGap": 720,
      "RestPriorityCallHrGap": 72,
      "NriIdentification": [ "91", "0", "392", "india", "+91" ],
      "InterestedNRestPntDedMintGap": 720,
      "CustomerOnCallDailerApi": "http://easydial123.paisabazaar.com/api/livecallcarob.php?",
      "CarExpiryDayGap": 7,
      "PooleventQExpMinutes": 1,
      "PriorityeventQExpMinutes": 0,
      "LastCallPCBDoneMintsGap": 10,
      "InvisibleTimeNANC_New": 15,
      "InvisibleTimeNANC_Revisit": 15,
      "Releaseleadsshowtime": 2800,
      "TodayCreatedExprTimeGap": 240,
      "BeforeTodayCreatedExprTimeGap": 240,
      "ExpiryLeadsShowCount": 2,
      "RevisitOrPaymentTimeMorning": 900,
      "RevisitOrPaymentTimeAfterMorning": 60,
      "PassiveRevisitEndMints": 1440,
      "PassiveCBEndMints": 1440,
      "ActiveCBEndTime": 15,
      "prioritySequence": [ "SERVICECB", "PAYMENTFAILURE", "2ndAttemptPAYMENTFAILURE", "ACTIVEREVISIT", "2NDATTEMPTACTIVEREVISIT", "PaymentCB", "2ndAttemptPCB", "EmailRevisit", "ActiveCB", "2ndAttemptActiveCB", "PASSIVEREVISIT", "2NDATTEMPTPASSIVEREVISIT", "PassiveCB", "2ndAttemptPassiveCB", "TODAYEXPIRY", "2NDATTEMPTTODAYEXPIRY", "EXPIRY0104DAYS", "EXPIRY0412DAYS", "EXPIRY1230DAYS", "EXPIRY3045DAYS", "REST", "PassiveNew", "MissedCB", "Unanswered" ],
      "IsCBRestrictionON": true,
      "UnAnsweredDaysGap": 1,
      "Week1MaxAttempts": 10,
      "WeekMaxAttempts": 5,
      "MaxAttempts": 20,
      "AnsWeekAttempts": 4,
      "restprioritylogicGroups": [ 1414 ],
      "TodayExpCall": [ "2020-03-10" ],
      "BMSCBShowTime": 15,
      "BMSCBEndTime": 90
    }
  ]
}