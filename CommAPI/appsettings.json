﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ValidHeaders": [
    "agentid",
    "asterisktoken"
  ],
  "ValidOrigins": [
    "https://progressiveqa.paisabazaar.com",
    "localhost",
    "https://mobilematrix.paisabazaar.com",
    "https://matrix.paisabazaar.com",
    "https://matrixliveapi.paisabazaar.com",
    "https://mobilematrixapi.paisabazaar.com",
    "https://claim.paisabazaar.com",
    "https://pbsupport.paisabazaar.com",
    "https://matrixdashboard.paisabazaar.com",
    "https://verification.paisabazaar.com",
    "https://bms.paisabazaar.ae",
    "https://pbmeet.paisabazaar.com"
  ],
  "AllowedHosts": "*",
  "Communication": {
    "Environment": "LIVE",
    "ConnectionString": {
      "DEV": "Data Source=pbmatrix-staging.database.windows.net,1433;Initial Catalog=PBCROMA;User Id=pbadmin;Password=***********",
      "UAT": "Data Source=pbmatrix-staging.database.windows.net,1433;Initial Catalog=PBCROMA;User Id=pbadmin;Password=***********",
      "LIVE": ""
    },
    "ProductDBConnection": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "ReplicaConnectionString": {
      "DEV": "Data Source=pbmatrix-staging.database.windows.net,1433;Initial Catalog=PBCROMA;User Id=pbadmin;Password=***********",
      "UAT": "Data Source=pbmatrix-staging.database.windows.net,1433;Initial Catalog=PBCROMA;User Id=pbadmin;Password=***********",
      "LIVE1": "",
      "LIVE": ""
    },
    "RedisConnection": {
      "DEV": "***********",
      "UAT": "***********",
      "LIVE": ""
    },
    "MatrixRedisConnection": {
      "DEV": "***********",
      "UAT": "***********",
      "LIVE": ""
    },
    "MongoDBConnection": {
      "DEV": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "UAT": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "LIVE": ""
    },
    "MongoGridFSConnection": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "MongoLogDBConnection": {
      "DEV": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "UAT": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "LIVE": ""
    },
    "ChatDBConnection": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "OneLeadDBConnection": {
      "DEV": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "UAT": "************************************************************************;socketTimeoutMS=8000&readPreference=secondaryPreferred",
      "LIVE": ""
    },
    "RealTimeDBConnection": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "KafkaBootstrapServers": {
      "DEV": "10.220.10.7:9092",
      "UAT": "10.220.10.7:9092",
      "LIVE": ""
    },
    "KafkaUserName": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "KafkaPassword": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "AWSSecretEnvironment": {
      "DEV": "",
      "UAT": "",
      "LIVE": ""
    },
    "IsAWSSceretEnabled": {
      "DEV": "false",
      "UAT": "false",
      "LIVE": "false"
    },
    "RedisPassword": {
      "DEV": "***********",
      "UAT": "***********",
      "LIVE": ""
    },
    "MatrixRedisPassword": {
      "DEV": "***********",
      "UAT": "***********",
      "LIVE": ""
    },
    "RealTimeDB": "",
    "MongoDBCommunicationDB": "",
    "MongoGridFSDB": "",
    "MongoDBLogging": "",
    "ChatDB": "",
    "ChatDBcommon": "",
    "OneLeadDB": "oneLeadDB",
    "SaltKey": "",
    "InitializationVectorKey": "",
    "SQSLogingQueueUrl": "",
    "CJ_Key": "",
    "CJ_IV": ""
  }
}